import ClearIcon from '@mui/icons-material/Clear';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import { Box, Grid, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsAccordion,
  NsButton,
  NsTag,
  NsForm,
  useFormContext,
  NsAccordionDetails,
} from '@netservice/astrea-react-ds';
// import { NsAccordionProps } from '@netservice/astrea-react-ds/dist/cjs/components/components/NsAccordion';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'relay-forms';
import APIWrapper from 'src/utils/APIWrapper';
import { getSiglaSezione } from 'src/utils/Authentication';
import {
  InputPayloadBackend,
  MonitoraggioDataProps,
  PageInfoProps,
} from '../interfaces';
import { getDateInMilliseconds, stateMap } from '../shared/Utils';
import MonitoraggioTable from './MonitoraggioTable';
import { CompColleggioUdienza } from './CompColleggioUdienza';

const Monitoraggio: React.FC = () => {
  const { onSubmit, onReset } = useFormContext();
  const [resetQueryParams, setResetQueryParams] = useState<boolean>(false);

  const handleResetQueryParams = () => {
    setResetQueryParams(false);
  };

  const CustomFormButtons: React.FC = () => {
    const { submit, reset } = useForm<any>({
      onSubmit: (values) => {
        handleApplyFilters(values);
        setIsAccordionExpanded(false);
        setActiveFilters(values);
        onSubmit(values);
      },
    });

    const doReset = () => {
      onReset?.();
      reset();
      setIsSezioneSelected(false);
      setSelectedSezione(null);
      setResetQueryParams(true);
    };

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <NsButton
          variant="contained"
          color="secondary"
          onClick={doReset}
          sx={{ display: 'inline-block' }}
          disabled={!isSezioneSelected}
        >
          {t('monitoraggio.pulisciCampi')}
        </NsButton>
        <NsButton
          variant="contained"
          color="primary"
          onClick={submit}
          sx={{ display: 'inline-block' }}
          disabled={!isSezioneSelected}
        >
          {t('monitoraggio.ricerca')}
        </NsButton>
      </div>
    );
  };

  const [activeFilters, setActiveFilters] = useState<any>([]);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState(true);
  const [filteredData, setFilteredData] = useState<MonitoraggioDataProps[]>([]);
  const [resultMessage, setResultMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(true);
  const [isSezioneSelected, setIsSezioneSelected] = useState<boolean>(false);
  const [selectedSezione, setSelectedSezione] = useState<string | null>(null);
  const [criteriaParameters, setCriteriaParameters] = useState<any>({});

  const [pageInfoRequest, setPageInfoRequest] = useState<PageInfoProps>({
      pageNumber: 0,
      pageSize: 25,
      order: {
        propertyName: 'dataUdienza',
        asc: true,
      }
  })

  const [pageInfoServer, setPageInfoServer] = useState<PageInfoProps>({})
  

  const { t } = useTranslation();

  const siglaSezione = getSiglaSezione();

  const handleSezioneChange = (sezione: any) => {
    const sezioneValue = sezione?.value || null;
    setSelectedSezione(sezioneValue);
    setIsSezioneSelected(!!sezioneValue);
  };

  const handleApplyFilters = (
    onSubmitData: any,
  ) => {
    const newCriteriaParametes = buildCriteriaParameters(onSubmitData);
    const pageInfo = {...pageInfoRequest, pageNumber: 0}; 
    setPageInfoRequest(pageInfo);
    setCriteriaParameters({...criteriaParameters, ...newCriteriaParametes})
  
    const requestBody: InputPayloadBackend = {
      pageInfo: pageInfo, 
      criteriaParameters: newCriteriaParametes
    };
    getData(requestBody);    
  }

  const handleApplyPagination = (
      newPageInfo: PageInfoProps,
    ) => {
      const pageInformation: PageInfoProps = {...pageInfoRequest, ...newPageInfo}   
      setPageInfoRequest(pageInformation);
      const requestBody: InputPayloadBackend = {
        pageInfo: pageInformation, 
        criteriaParameters: criteriaParameters
      };
      getData(requestBody); 
    }

  const handleApplySorting = (property: string) => {
    const isAsc = !(pageInfoRequest.order?.propertyName === property && pageInfoRequest.order?.asc);
    const newOrder: PageInfoProps = {
      order: {
        propertyName: property,
        asc: isAsc,
      } 
    };
    const pageInfo = {...pageInfoRequest, ...newOrder};
    handleApplyPagination(pageInfo);
  };

  useEffect(() => {
    if (siglaSezione !== 'CE') {
      setSelectedSezione(siglaSezione);
      setIsSezioneSelected(true);
    }
  }, [siglaSezione]);

  const buildCriteriaParameters = (onSubmitData?: any) => {
    const dataUdienzaDa = onSubmitData?.dataUdienzaDa;
    const dataUdienzaA = onSubmitData?.dataUdienzaA;
    const dataMinutaDa = onSubmitData?.dataMinutaDa;
    const dataMinutaA = onSubmitData?.dataMinutaA;
    const dataPubblicazioneDa = onSubmitData?.dataPubblicazioneDa;
    const dataPubblicazioneA = onSubmitData?.dataPubblicazioneA;
    const tipoUdienza = onSubmitData?.tipoUdienza?.label;
    const collegio = onSubmitData?.collegio?.value;
    const numeroRicorsoDa = onSubmitData?.nrgDa;
    const numeroRicorsoA = onSubmitData?.nrgA;
    const cognomeParte = onSubmitData?.cognomeParte;
    const annoRicorsoDa = onSubmitData?.nrgDa2;
    const annoRicorsoA = onSubmitData?.nrgA2;
    const cfEstensore = onSubmitData?.estensore?.value;
    const cfPresidente = onSubmitData?.presidente?.value;

    let sezione = null;

    if (siglaSezione !== 'CE') {
      sezione = siglaSezione;
    } else {
      sezione = selectedSezione;
    }

    const criteriaParameters = {
      dataUdienzaDa: getDateInMilliseconds(dataUdienzaDa),
      dataUdienzaA: getDateInMilliseconds(dataUdienzaA),
      dataMinutaDa: getDateInMilliseconds(dataMinutaDa),
      dataMinutaA: getDateInMilliseconds(dataMinutaA),
      dataPubblicazioneDa: getDateInMilliseconds(dataPubblicazioneDa),
      dataPubblicazioneA: getDateInMilliseconds(dataPubblicazioneA),
      tipoUdienza: tipoUdienza,
      sezione: sezione,
      collegio: collegio,
      numeroRicorsoDa: numeroRicorsoDa,
      numeroRicorsoA: numeroRicorsoA,
      cognomeParte: cognomeParte,
      annoRicorsoDa: annoRicorsoDa,
      annoRicorsoA: annoRicorsoA,
      cfPresidente: cfPresidente,
      cfEstensore: cfEstensore,
    };
    return criteriaParameters;
  };

  const mapMonitoraggioData = (penaleUdienzeView: any) => {
    return penaleUdienzeView?.map((item: any) => {
      return {
        idCat: item?.idCat,
        sezione: item?.sezione,
        dataUdienza: item?.dataUdienza,
        tipoUdienza: item?.tipoUdienza,
        collegio: item?.collegio,
        presidente: item?.presidente,
        relatore: item?.relatore,
        estensore: item?.estensore,
        parti: item?.detParti,
        numeroRicorso: item?.numeroRicorso,
        annoRicorso: item?.annoRicorso,
        tipoRicorso: item?.tipoRicorso,
        stato: stateMap[item?.stato],
        dataMinuta: item?.dataMinuta,
        totaleGiorni: item?.totaleGiorni,
        annoSezionale: item?.annoSezionale,
        numeroSezionale: item?.numeroSezionale,
        idProvvedimento: item?.idProvvedimento,
        idSentenza: item?.idSentenza,
        annoPadre: item?.ricercaRiunitiView?.annoPadre,
        numeroPadre: item?.ricercaRiunitiView?.numeroPadre,
        riunito: item?.ricercaRiunitiView?.riunito,
        isPrincipale: item?.ricercaRiunitiView?.principale,
        idUdienza: item?.penaleUdienzeViewPK?.idUdienza,
        nrg: item?.penaleUdienzeViewPK?.nrg,
        idRicUdien:item?.penaleUdienzeViewPK?.idRicUdien,
        ricercaRiunitiView: item.ricercaRiunitiView,
        nraccg: item.nraccg          
      };
    });
  }

  const getData = async (filteredRequestBody: any) => {
    if (!filteredRequestBody) {
      return;
    }
    try {
      const api = await APIWrapper;

      Object.entries(filteredRequestBody.criteriaParameters).forEach(
        ([key, value]) => {
          if (value === '' || value === undefined) {
            filteredRequestBody.criteriaParameters[key] = null;
          }
        }
      );

      setIsLoading(true);
      const { message, response } = await api.call(
        'POST',
        'monitoraggio',
        filteredRequestBody
      );

      const mapped = mapMonitoraggioData(response?.content);
      setPageInfoServer(response?.pageInfo);
      setResultMessage(message);      
      setFilteredData(mapped);
    } catch (error: any) {
      console.log('error', error);
    } finally {
      setIsLoading(false);
    }
  };

  const MonitoraggioContent = () => {
    const clearFields = () => {
      setActiveFilters([]);
      setIsSezioneSelected(false);
      setSelectedSezione(null);
    };

    return (
      <>
        <NsForm
          buttonsSlot={<CustomFormButtons />}
          onReset={() => {
            clearFields();
            onReset?.();
          }}
          onSubmit={(onSubmitData: any) => {
            onSubmit(onSubmitData);
          }}
        >
          <CompColleggioUdienza
            setIsSezioneSelected={setIsSezioneSelected}
            isSezioneSelected={isSezioneSelected}
            resetQueryParams={resetQueryParams}
            onResetQueryParams={handleResetQueryParams}
            selectedSezione={selectedSezione || ''}
            onSezioneChange={handleSezioneChange}
          />
        </NsForm>
        <Grid item xs={12}>
          <Typography mt={3} mb={1} variant="body2" sx={{ display: 'flex' }}>
            <span style={{ color: 'red', fontSize: '1.2rem' }}>*</span>
            {t('monitoraggio.form.notaFiltriRicerca')}
          </Typography>
        </Grid>
      </>
    );
  };

  const renderActiveFilters = () => {
    let isTypographyRendered = false;
    return Object.entries(activeFilters).map(([key, value]) => {
      if (value !== null && value !== '' && value !== undefined) {
        let label = '';
        if (typeof value === 'object' && 'label' in value) {
          label = `${t('form.filters.' + key)}: ${value.label}`;
        } else {
          label = `${t('form.filters.' + key)}: ${value}`;
        }
        if (!isTypographyRendered) {
          isTypographyRendered = true;
          return (
            <Box key={key} m={2} component={'span'} mb={2} mr={2}>
              <Typography sx={{ fontWeight: 'bold' }}>
                Filtri attivi:{' '}
              </Typography>
              <NsTag
                label={label}
                color="primary"
                variant="outlined"
                clickable={true}
                onDelete={() => handleDelete(key)}
                deleteIcon={<ClearIcon />}
              />
            </Box>
          );
        } else {
          return (
            <Box key={key} m={2} component={'span'} mb={2} mr={2}>
              <NsTag
                label={label}
                color="primary"
                variant="outlined"
                clickable={true}
                onDelete={() => handleDelete(key)}
                deleteIcon={<ClearIcon />}
              />
            </Box>
          );
        }
      }
    });
  };

  const handleDelete = (key: string) => {
    if (key === 'sezione') {
      return;
    }

    setActiveFilters((prevFilters: any) => {
      const newFilters = { ...prevFilters };
      delete newFilters[key];
      const nonNullKeysLength = Object.keys(newFilters).filter(
        (key) => newFilters[key] && newFilters[key] !== ''
      ).length;
      if (nonNullKeysLength === 0) {
        setShowFilters(true);
        setIsAccordionExpanded(true);
      }
      if (nonNullKeysLength > 0) {
        handleApplyFilters(newFilters);
      }
      return newFilters;
    });
  };

  return (
    <>
      <Typography mt={2} mb={4} variant="h1" style={{ fontSize: '3rem' }}>
        {t('monitoraggio.monitoraggioUdienze')}
      </Typography>
      <Box sx={{ border: '1px solid grey' }}>
        <NsAccordion
          title="Criteri di ricerca"
          expanded={isAccordionExpanded}
          onChange={(event: React.SyntheticEvent, isExpanded: boolean) => {
            setIsAccordionExpanded(isExpanded);
          }}
          icon={<FilterAltIcon sx={{ color: 'white' }} />}
          typographyProps={{
            sx: {
              fontWeight: 'bold',
              fontSize: '1.2rem',
              color: 'white',
            },
          }}
        >
          <NsAccordionDetails>
            <MonitoraggioContent />
          </NsAccordionDetails>
        </NsAccordion>
      </Box>
      {<NsFullPageSpinner isOpen={isLoading} value={100} />}
      {!isAccordionExpanded && !isLoading && (
        <>
          {showFilters && <Box mb={2}>{renderActiveFilters()}</Box>}
          <MonitoraggioTable
            data={filteredData}
            resultMessage={resultMessage}
            hideFilters={() => setShowFilters(false)}
            pageInfoServer={pageInfoServer}
            actionApplyPagination={handleApplyPagination}
            actionApplySorting={handleApplySorting}
          />
        </>
      )}
    </>
  );
};

export default Monitoraggio;
