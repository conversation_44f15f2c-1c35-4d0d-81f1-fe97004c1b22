import React, { useEffect, useState, useCallback } from 'react';
import { Grid } from '@mui/material';
import { useNotifier } from '@netservice/astrea-react-ds';
import APIWrapper from 'src/utils/APIWrapper';
import { CompColleggioUdienzaProps } from '../interfaces';
import DatiUdienza from './DatiUdienza';
import DatiProvvedimento from './DatiProvvedimento';

export const CompColleggioUdienza = ({
  setIsSezioneSelected,
  isSezioneSelected,
  resetQueryParams,
  onResetQueryParams,
  selectedSezione,
  onSezioneChange,
}: CompColleggioUdienzaProps) => {
  const { notify } = useNotifier();
  const [listaEstensori, setListaEstensori] = useState<any>({});
  const [listaPresidenti, setListaPresidenti] = useState<any>({});

  const [otherFilters, setOtherFilters] = useState<any>({});

  const buildQueryString = (params: Record<string, any>): string => {
    const queryParts = [];
    for (const key in params) {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
      }
    }
    return queryParts.join('&');
  };

  const getLista = useCallback(async (sezione: string | null, currentFilters: any, endpoint: string) => {
    if (!sezione) {
      return {};
    }

    try {
      const allParams = { ...currentFilters, sezione: sezione };
      const queryString = buildQueryString(allParams);
      const url = `monitoraggio/${endpoint}${queryString ? `?${queryString}` : ''}`;

      const api = await APIWrapper;
      const response = await api.call('GET', url);
      return response || {};

    } catch (error: any) {

      notify({
        type: 'error',
        message: `Errore nel caricamento ${endpoint === 'listaEstensori' ? 'estensori' : 'presidenti'} per la sezione ${sezione}.`,
      });
      return {};
    }
  }, [notify]);

  useEffect(() => {

    if (selectedSezione) {
      getLista(selectedSezione, otherFilters, 'listaEstensori').then(setListaEstensori);
      getLista(selectedSezione, otherFilters, 'listaPresidenti').then(setListaPresidenti);
    } else {
      setListaEstensori({});
      setListaPresidenti({});
    }

  }, [selectedSezione, otherFilters, getLista]);

  const updateOtherFilters = useCallback((data: any) => {
    const { sezione, ...restOfData } = data;

    setOtherFilters((prevFilters: any) => {
      const newFilters = { ...prevFilters, ...restOfData };
      return newFilters;
    });
  }, []);

  useEffect(() => {
    if (resetQueryParams) {

      setOtherFilters({});
      onResetQueryParams();
    }
  }, [resetQueryParams, onResetQueryParams]);

  return (
    <Grid container spacing={4}>
      <DatiUdienza
        changedSezione={setIsSezioneSelected}
        isSezioneSelected={isSezioneSelected}
        searchedQuery={updateOtherFilters}
        listaPresidenti={listaPresidenti}
        selectedSezione={selectedSezione}
        onSezioneChange={onSezioneChange}
      />
      <DatiProvvedimento
        disabledFields={isSezioneSelected}
        listaEstensori={listaEstensori}
        searchedQuery={updateOtherFilters}
      />
    </Grid>
  );
};