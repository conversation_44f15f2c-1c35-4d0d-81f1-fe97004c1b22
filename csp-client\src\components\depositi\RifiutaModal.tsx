import { TextareaAutosize } from '@mui/base';
import { Box, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier 
} from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';

interface RifiutaModal {
  id?: any;
  closeModal: any;
  closeDrawer?: () => void;
  refreshData?: () => void;
  refreshTable?: () => void;
}

export default function RifiutaModal(props: RifiutaModal) {
  const { id, closeModal, closeDrawer, refreshData, refreshTable } = props;
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const router = useRouter();
  const [motivazione, setMotivazione] = useState<string>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const rifiuta = useCallback(async () => {
    try {
      setIsLoading(true);
      const body = {
        idDeposito: id,
        motivazione: motivazione,
      };

      if (!motivazione)
        return notify({
          type: 'warning',
          message: t('form.errors.compilareLeMotivazioni'),
        });
      const api = await APIWrapper;
      await api.call('POST', `accettazioneDepositi/rifiuta`, body);
      notify({ type: 'success', message: 'Rifiutato' });
      closeModal();
      if (closeDrawer) {
        closeDrawer();
      }
      if (refreshTable) {
        refreshTable();
      }
      if (refreshData) {
        router.push('/depositi');
        refreshData();
      }
    } catch (err: any) {
      console.log('error', err);
    } finally {
      setIsLoading(false);
    }
  }, [id, closeModal, closeDrawer, motivazione]);

  return (
    <>
      <Box>
        <Typography mb={1}>{t('common.confermandoRifiutaDeposito')}</Typography>
        <form>
          <TextareaAutosize
            style={{ width: '100%' }}
            minRows={6}
            name="motivazione"
            required
            placeholder="*Inserisci una motivazione o una richiesta di modifica"
            onChange={(e) => setMotivazione(e.target.value)}
          />
          <NsButton
            size="small"
            sx={{ marginTop: '10px' }}
            variant="contained"
            onClick={rifiuta}
          >
            {t('buttonsLabel.conferma')}
          </NsButton>
        </form>
      </Box>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
    </>
  );
}
