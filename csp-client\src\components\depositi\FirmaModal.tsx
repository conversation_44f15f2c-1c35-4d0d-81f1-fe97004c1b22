import { Grid, InputAdornment, TextField, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { FirmaModalProps } from '../interfaces';

export default function FirmaModal({
  id,
  closeModal,
  closeDrawer,
  refreshData,
  azureToken,
  isFirmaOtp,
}: Readonly<FirmaModalProps>) {
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [otp, setOtp] = useState<string>('');
  const [showOTP, setShowOTP] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const toggleOTPVisibility = () => {
    setShowOTP(!showOTP);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleConferma = async () => {
    try {
      setIsLoading(true);
      let pinCode = '';
      if (isFirmaOtp) {
        pinCode = otp;
      } else {
        pinCode = azureToken ?? '';
      }

      if (!username || !password || !pinCode) {
        setIsLoading(false);
        notify({ type: 'error', message: 'Dati obbligatori non inseriti!' });
      } else {
        const api = await APIWrapper;
        const response = await api.call(
          'POST',
          `firmaDepositi/firmaRemotaProvvedimento?idDeposito=${id}&username=${username.trim()}&password=${password.trim()}&otppassword=${pinCode.trim()}`
        );

        if (response) {
          const responseInvioUCU = await api.call(
            'GET',
            `invocaPubblicazione/inviaUCU?idDeposito=${id}`
          )
          if(responseInvioUCU) {
            notify({
              type: 'warning',
              message: t('form.warnings.aggiornareSIC'),
            });
            notify({ type: 'success', message: 'Accettato e pubblicato' });
          } else {
            notify({
              type: 'warning',
              message: t('form.errors.invioUCUError'),
            });
          }          
          closeModal();
          if (closeDrawer) {
            closeDrawer();
          }
          if (refreshData) {
            refreshData();
          }
        } else {
          notify({
            type: 'error',
            message: "Rivolgersi all'amministratore del sistema",
          });
          closeModal();
          if (closeDrawer) {
            closeDrawer();
          }
        }
      }
    } catch (err: any) {
      console.log('error', err);
      if (err?.message?.includes('Errori durante la pubblicazione.')) {
        closeModal();
        if (closeDrawer) {
          closeDrawer();
        }
        if (refreshData) {
          refreshData();
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const hideHover = {
    ':hover': {
      backgroundColor: 'white',
    },
  };

  return (
    <>
      <Grid container>
        <Grid item xs={12} mt={3}>
          <Typography mb={1}>{t('depositi.firmaModal.username')}</Typography>
          <TextField
            size="small"
            type="text"
            value={username}
            label=""
            name="username"
            sx={{ minWidth: 300 }}
            onChange={(e) => setUsername(e.target.value)}
          />
          <Typography mb={1}>{t('depositi.firmaModal.password')}</Typography>
          <TextField
            size="small"
            type={showPassword ? 'text' : 'password'}
            value={password}
            label=""
            name="password"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <NsButton
                    sx={hideHover}
                    onClick={togglePasswordVisibility}
                    variant="text"
                  >
                    {showPassword
                      ? t('form.buttons.nascondi')
                      : t('form.buttons.mostra')}
                  </NsButton>
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
            onChange={(e) => setPassword(e.target.value)}
          />
          {isFirmaOtp && <Typography mb={1}>{t('depositi.otp')}</Typography>}
          {isFirmaOtp && (
            <TextField
              size="small"
              value={otp}
              label=""
              name="otp"
              type={showOTP ? 'text' : 'password'}
              sx={{ minWidth: 300 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <NsButton
                      sx={hideHover}
                      onClick={toggleOTPVisibility}
                      variant="text"
                    >
                      {showOTP
                        ? t('form.buttons.nascondi')
                        : t('form.buttons.mostra')}
                    </NsButton>
                  </InputAdornment>
                ),
              }}
              onChange={(e) => setOtp(e.target.value)}
            />
          )}
          <Grid container mt={3} justifyContent={'space-between'}>
            <NsButton
              variant="outlined"
              color="primary"
              onClick={closeModal}
              type="button"
            >
              {t('depositi.firmaModal.annulla')}
            </NsButton>
            <NsButton variant="contained" onClick={handleConferma}>
              {t('depositi.firmaModal.firmaEDeposita')}
            </NsButton>
          </Grid>
          {/*<ValidatedDatePicker size="small" name="test" label="Data Udienza" />*/}
        </Grid>
      </Grid>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
    </>
  );
}
