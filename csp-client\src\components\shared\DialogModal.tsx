import { Typography } from '@mui/material';
import {
  NsDialog,
  NsDialogActions,
  NsDialogContent,
  NsDialogTitle,
} from '@netservice/astrea-react-ds';

export default function DialogModal({
  isOpen,
  onClose,
  title,
  content,
  maxWidth,
  fullScreen,
}: any) {
    return (
    <NsDialog
      maxWidth={maxWidth}
      setOpen={onClose}
      open={isOpen}
      fullScreen={fullScreen}
    >
      <NsDialogTitle setOpen={() => onClose()} closeButton='Chiudi'>
      <Typography variant="h2" >
            {title}
      </Typography>
      </NsDialogTitle>
      <NsDialogContent>{content}</NsDialogContent>
      <NsDialogActions
        setOpen={() => onClose()}
      ></NsDialogActions>
    </NsDialog>
  );
}
