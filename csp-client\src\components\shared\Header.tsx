import React, { useEffect } from 'react';
import {
  NsHeader,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/router';
import { getStoredToken, logout } from 'src/utils/Authentication';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import DownloadIcon from '@mui/icons-material/Download';
import { Box, Typography } from '@mui/material';

export interface HeaderProps {
  logo: string;
  entries: MenuEntry[];
}

export interface MenuValue {
  kind: 'single';
  link: string;
  label: string;
}

export interface MenuDropdown {
  kind: 'multiple';
  label: string;
  values: MenuValue[];
}

export type MenuEntry = MenuValue | MenuDropdown;

export function Header({ logo, entries }: HeaderProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { notify } = useNotifier();

  const packageJson = require('../../../package.json');
  const version = packageJson.version;

  const userPanelMenuItems = [
      {
       name: 'Info',
       path: '/info',
       icon: <DownloadIcon />,
     },
  ];


  const title = {
    bold: t('header.labels.title'),
    subtitle: version,
  };

  const logoutHandler = () => logout(router, null);

  const logoImage = () => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <img
          src="/images/logo-repubblica-giustizia-colored.png"
          alt={t('header.logo.alt')}
          style={{
            height: '55px',
            objectFit: 'cover',
            width: '48px',
            cursor: 'pointer',
          }}
        />
        <Box sx={{ display: 'flex', flexDirection: 'column', color: 'white', marginLeft: '15px' }}>
          <Typography variant="h1" className="header-title">
            CSP Client Corte Suprema di Cassazione
          </Typography>
          <Typography variant="caption" sx={{ alignSelf: 'flex-start' }}>
            {packageJson.version}
          </Typography>
        </Box>
      </Box>
    );
  };

  const token = getStoredToken();

  const menuItems = token
    ? [
        {
          name: t('common.gestioneDepositi'),
          path: '/depositi',
        },
        {
          name: t('common.ricercaRicorsi'),
          path: '/fascicoli',
        },
        {
          name: t('common.monitoraggio'),
          path: '/monitoraggio',
        },
      ]
    : [];

  return (
    <NsHeader
      menuItems={menuItems}
      onLogout={logoutHandler}
      //TODO passare il titolo nel componente e non da logoImage()
      //title={title}
      logo={logoImage()}
      router={router}
      userPanelMenuItems={userPanelMenuItems} type={'horizontal'} configuration={{
        centralLogo: undefined,
        dropDownConfiguration: undefined
      }}    />
  );
}
