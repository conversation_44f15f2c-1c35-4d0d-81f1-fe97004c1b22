import { Box, Checkbox, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { TFunction } from 'i18next';
import { useTheme } from '@mui/material/styles';
import { useState, useEffect } from 'react';
import { getDefaultFrontendSorting } from '../constants/depositiSorting';

// Styles for diagnosis circles
const diagnoziStyle = {
  width: '30px',
  height: '30px',
  padding: '5px',
  borderRadius: '50%',
  textAlign: 'center',
  cursor: 'pointer',
  color: '#FFFFFF',
  marginRight: '10px',
};

const checkboxStyle = {
  '& .MuiSvgIcon-root': {
    width: '24px',
    height: '24px',
  },
  padding: 0,
};

interface ColumnSize {
  [key: string]: number;
}

const columnSizes: ColumnSize = {
  select: 50,
  sezioneUdienza: 60,
  tipo: 60,
  dataudienza: 100,
  dePlano: 70,
  collegio: 60,
  nrg: 100,
  depositante: 180,
  atto: 150,
  presidente<PERSON><PERSON><PERSON><PERSON>: 160,
  data: 140,
  oscuramentoSic: 120,
  oscuramentoDeskCsp: 165,
  inCaricoDa: 180,
  statoDeposito: 110,
  dataLavorazione: 140,
  numRaccGenerale: 120,
  diagnosi: 160
};

export interface DepositiColumnsConfig {
  baseColumns: any[];
  groupedColumns: any[];
  initialSorting: any[];
}

export default function useDepositiColumns(
  t: TFunction,
  handleModal: (param: string, data?: any) => void,
  handleSlideModal: (row: any) => void,
  selectedRows: string[],
  setSelectedRows: (rows: string[]) => void
): DepositiColumnsConfig {
  const theme: any = useTheme();

  // State to track if "select all" is active and excluded rows
  const [selectAllActive, setSelectAllActive] = useState(false);
  const [excludedRows, setExcludedRows] = useState<string[]>([]);

  // Reset states when filters change (selectedRows becomes empty)
  useEffect(() => {
    if (selectedRows.length === 0) {
      setSelectAllActive(false);
      setExcludedRows([]);
    }
  }, [selectedRows]);

  const enhanceColumnsWithClickHandler = (columns: any[]) => {
    return columns.map(column => {
      if (column.accessorKey === 'select') return column;

      return {
        ...column,
        cell: (props: any) => {
          const originalCell = column.cell ? column.cell(props) : props.getValue();
          return (
            <div
              onClick={() => handleSlideModal(props.row.original)}
              style={{ cursor: 'pointer', width: '100%', height: '100%', padding: '8px' }}
            >
              {originalCell}
            </div>
          );
        }
      };
    });
  };

  const baseColumns = [
    {
      accessorKey: 'select',
      header: ({ table }: any) => {
        const allVisibleRows = table.getFilteredRowModel().rows;
        const allVisibleIds = allVisibleRows.map((row: any) => row.original.id);

        // Determine checkbox state based on selection mode
        let isChecked: boolean = false;
        let isIndeterminate: boolean = false;

        if (selectAllActive) {
          // In "select all" mode, check if any visible rows are excluded
          const anyExcluded = allVisibleIds.some((id: string) => excludedRows.includes(id));
          isChecked = !anyExcluded;
          isIndeterminate = anyExcluded && excludedRows.length < allVisibleIds.length;
        } else {
          // In normal mode, check if all visible rows are selected
          const allSelected = allVisibleIds.length > 0 &&
                             allVisibleIds.every((id: string) => selectedRows.includes(id));
          const someSelected = allVisibleIds.some((id: string) => selectedRows.includes(id));

          isChecked = allSelected;
          isIndeterminate = someSelected && !allSelected;
        }

        return (
          <Box display="flex" justifyContent="center" alignItems="center" width="100%" height="100%" marginLeft={1}>
            <Checkbox
              checked={isChecked}
              indeterminate={isIndeterminate}
              onChange={() => {
                if (selectAllActive || isChecked) {
                  // Deselect all
                  setSelectAllActive(false);
                  setExcludedRows([]);
                  setSelectedRows([]);
                } else {
                  // Select all
                  setSelectAllActive(true);
                  setExcludedRows([]);
                  setSelectedRows([...new Set([...selectedRows, ...allVisibleIds])]);
                }
              }}
              sx={checkboxStyle}
            />
          </Box>
        );
      },
      cell: ({ row }: any) => {
        if (row.original.isDepositoLavoratoDaSic)
          return <></>;
        const rowId = row.original.id;

        // Determine if row is selected based on current mode
        const isSelected = selectAllActive
          ? !excludedRows.includes(rowId)
          : selectedRows.includes(rowId);

        // Auto-select newly loaded rows in "select all" mode
        if (selectAllActive && !selectedRows.includes(rowId) && !excludedRows.includes(rowId)) {
          setTimeout(() => {
            setSelectedRows([...selectedRows, rowId]);
          }, 0);
        }
        return (
          <Box display="flex" justifyContent="center" alignItems="center" width="100%" height="100%" marginLeft={1}>
            <Checkbox
              checked={isSelected}
              onChange={() => {
                if (selectAllActive) {
                  if (isSelected) {
                    // Exclude this row
                    setExcludedRows([...excludedRows, rowId]);
                    setSelectedRows(selectedRows.filter((id: string) => id !== rowId));
                  } else {
                    // Include this row
                    setExcludedRows(excludedRows.filter((id: string) => id !== rowId));
                    if (!selectedRows.includes(rowId)) {
                      setSelectedRows([...selectedRows, rowId]);
                    }
                  }
                } else {
                  // Toggle selection in normal mode
                  setSelectedRows(
                    isSelected
                      ? selectedRows.filter((id: string) => id !== rowId)
                      : [...selectedRows, rowId]
                  );
                }
              }}
              sx={checkboxStyle}
            />
          </Box>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'sezioneUdienza',
      header: t('common.sezione'),
      enableSorting: false,
    },
    {
      accessorKey: 'dataudienza',
      header: t('common.data'),
      enableSorting: true,
    },
    {
      accessorKey: 'dePlano',
      header: 'De plano',
      cell: ({ row }: any) => (row.original.dePlano == 1 ? 'Sì' : 'No'),
      enableSorting: false,
    },
    {
      accessorKey: 'tipo',
      header: t('common.tipo'),
      enableSorting: false,
    },
    {
      accessorKey: 'collegio',
      header: t('common.collegio'),
      enableSorting: false,
    },
    {
      accessorKey: 'nrg',
      header: t('calendario.nrg'),
      cell: ({ row }: any) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }} className={'ns-display-2col'}>
          <Box>
            <Typography variant="body2" color="text.primary">
              {row.original.nrg}
            </Typography>
          </Box>
          <Box>
            {row.original.ricercaRiunitiView?.principale && (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() => handleModal('riuniti', row.original.ricercaRiunitiView)}
              >
                {t('common.principale')}
              </NsButton>
            )}
          </Box>
        </Box>
      ),
      enableSorting: true,
    },
    {
      accessorKey: 'depositante',
      header: t('common.depositante'),
      enableSorting: true,
    },
    {
      accessorKey: 'atto',
      header: t('common.atto'),
      enableSorting: false,
    },
    {
      accessorKey: 'presidenteCollegio',
      header: t('common.presidenteCollegio'),
      enableSorting: false,
    },
    {
      accessorKey: 'data',
      header: t('common.data'),
      enableSorting: true,
    },
    {
      accessorKey: 'oscuramentoSic',
      header: t('common.oscuramentoSic'),
      cell: ({ row }: any) => (row.original.oscuramentoSic ? 'Sì' : 'No'),
      enableSorting: false,
    },
    {
      accessorKey: 'oscuramentoDeskCsp',
      header: t('common.oscuramentoDeskCsp'),
      cell: ({ row }: any) => (row.original.oscuramentoDeskCsp ? 'Sì' : 'No'),
      enableSorting: false,
    },
    {
      accessorKey: 'inCaricoDa',
      header: t('common.presoDa'),
      enableSorting: false,
    },
    {
      accessorKey: 'statoDeposito',
      header: t('common.statoDeposito'),
      enableSorting: false,
    },
    {
      accessorKey: 'dataLavorazione',
      header: t('depositi.dataLavorazione'),
      enableSorting: true,
    },
    {
      accessorKey: 'numRaccGenerale',
      header: t('depositi.numRaccGen'),
      enableSorting: true,
    },
    {
      accessorKey: 'diagnosi',
      header: t('depositi.diagnosi'),
      cell: ({ row }: any) => (
        <Box display="flex">
          {row.original.diagnosi.map((value: string, i: number) => {
            if (parseInt(value) > 0) {
              const customStyle: Record<number, { background: string; color: string }> = {
                0: { background: 'yellow', color: 'black' }, // WARNING
                1: { background: 'red', color: 'white' },   // ERROR
                2: { background: 'black', color: 'white' }, // FATAL
              };
              return (
                <Box
                  sx={{ ...diagnoziStyle, ...customStyle[i] }}
                  key={i}
                  onClick={() => handleSlideModal(row.original)}
                >
                  {value}
                </Box>
              );
            }
            return null;
          })}
        </Box>
      ),
      enableSorting: false,
    },
  ];

  const enhancedBaseColumns = enhanceColumnsWithClickHandler(baseColumns);

  const groupedColumns = [
    {
      id: 'datiUdienza',
      header: 'Dati Udienza',
      columns: enhancedBaseColumns
        .filter(col => ['select', 'sezioneUdienza', 'dataudienza', 'dePlano', 'tipo', 'collegio', 'nrg'].includes(col.accessorKey))
        .map(col => ({ ...col, size: columnSizes[col.accessorKey] }))
    },
    {
      id: 'datiDeposito',
      header: 'Dati Deposito',
      columns: enhancedBaseColumns
        .filter(col => ['depositante', 'atto', 'presidenteCollegio', 'data', 'oscuramentoSic',
          'oscuramentoDeskCsp', 'inCaricoDa', 'statoDeposito', 'dataLavorazione',
          'numRaccGenerale'].includes(col.accessorKey))
        .map(col => ({ ...col, size: columnSizes[col.accessorKey] }))
    },
    {
      id: 'esiti',
      header: 'Esiti',
      columns: enhancedBaseColumns
        .filter(col => ['diagnosi'].includes(col.accessorKey))
        .map(col => ({ ...col, size: columnSizes[col.accessorKey] }))
    },
  ];

  // Usa l'ordinamento di default centralizzato
  const initialSorting = getDefaultFrontendSorting();

  return {
    baseColumns: enhancedBaseColumns,
    groupedColumns,
    initialSorting
  };
}