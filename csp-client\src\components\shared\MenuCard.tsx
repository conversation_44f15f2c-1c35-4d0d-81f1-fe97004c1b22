import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { IconButton, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import React from 'react';

const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(0),
  color: theme.palette.text.secondary,
}));

const Description = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(4),
  paddingLeft: 0,
  textAlign: 'left',
  background: 'white',
  boxShadow: 'unset',
  fontSize: 18,
  color: 'black',
}));

const DescriptionItem = styled(Item)`
  background: #fefefe;
  padding: 0;
  box-shadow: unset;
  color: black;
  text-align: left;
`;

const IconItem = styled(Item)`
  background: #fefefe;
  box-shadow: unset;
  color: white;
  font-size: '10px';
`;

const IconArrowForward = styled(Item)`
  background: #edeae2;
  box-shadow: unset;
  color: #edeae2;
  height: 40px;
  width: 40px;
  text-align: center;
`;

const MenuCard = styled(Card)`
  height: 230px;
  background: inherit;
  background-color: #ffff;
  box-shadow: none;
  font-family: 'Titillium Web', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: #0057bc;
  text-align: left;
  border: none;
  border: 1px solid #d7d7d7;
`;

export interface MenuItemData {
  title: string;
  subtitle: string;
  link: string;
  icon: React.ReactElement;
  group?: string;
}

export interface MenuItemProps {
  item: MenuItemData;
}

export function MenuItem({ item }: MenuItemProps) {
  return (
    <div>
      <MenuCard>
        <Grid container pl={3} sx={{ cursor: 'pointer' }}>
          <Grid item mt={5} xs={12}>
            <Box sx={{ width: '70px' }}>
              <IconItem>{item.icon}</IconItem>
            </Box>
            <Box>
              <DescriptionItem>
                <Typography component="h2" variant="h2">
                  {item.title}
                </Typography>
                <Description>{item.subtitle}</Description>
              </DescriptionItem>
            </Box>
          </Grid>

          <Grid item xs={1}>
            <IconButton
              aria-label="arrow-forward"
              sx={{ padding: '0px' }}
              component="label"
            >
              <IconArrowForward>
                <ArrowForwardIcon
                  sx={{ color: '#A19268', paddingTop: '4px' }}
                  fontSize="large"
                />
              </IconArrowForward>
            </IconButton>
          </Grid>
        </Grid>
      </MenuCard>
    </div>
  );
}
