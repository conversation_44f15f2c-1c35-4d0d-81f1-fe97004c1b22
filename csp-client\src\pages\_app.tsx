import { AppProps } from 'next/app';
import '@fontsource/titillium-web/300.css';
import '@fontsource/titillium-web/400.css';
import '@fontsource/titillium-web/600.css';
import '@fontsource/titillium-web/700.css';

/*
 * Global CSS and other imports
 */
import '../styles/globals.css';
import '../styles/Depositi.css';
import {
  Backdrop,
  CircularProgress,
  CssBaseline,
  ThemeProvider,
} from '@mui/material';
import { theme } from 'src/theme/cassazioneTheme';
import axios from 'axios';
import { NextPage } from 'next';
import { appWithTranslation, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import { RelayEnvironmentProvider } from 'relay-hooks';
import initEnvironment from 'src/components/relay/createRelayEnvironment';
import { DefaultLayout } from 'src/layouts/DefaultLayout';
import { getStoredToken, logout, storeToken } from 'src/utils/Authentication';
import nextI18nConfig from '../../i18n/next-i18next.config';
import {
  CspClientConfiguration,
  CspClientConfigurationContext,
  useConfig,
} from '../components/shared/configuration.context';
import APIWrapper from '../utils/APIWrapper';
import Login from 'src/components/login/Login';
import { AxiosInteceptor } from '../utils/API';
import { NotificationProvider } from 'src/utils/NotificationProvider';
import LoginIndex from './login';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';

async function loadConf() {
  const res = await axios.get<CspClientConfiguration>(
    `${process.env.NEXT_PUBLIC_BASE_PATH || ''}/api/configuration`
  );
  if (res.status === 200) {
    return res.data;
  } else {
    throw res.statusText;
  }
}

export type NextPageWithLayout = NextPage & {
  getLayout?: (page: React.ReactElement) => React.ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

const MyApp: React.FC<AppPropsWithLayout> = (appProps: AppPropsWithLayout) => {
  const [conf, setConf] = useState<CspClientConfiguration | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);

  const { i18n } = useTranslation();
  const router = useRouter();

  useEffect(() => {
    loadConf()
      .then((res) => setConf(res))
      .catch((err) => {
        throw err;
      });
  }, []);

  useEffect(() => {
    const handleStart = (url: string) => {
      if (url.split('?')[0] !== router.asPath.split('?')[0]) {
        setIsNavigating(true);
      }
    };
    const handleComplete = (url: string) => {
        setIsNavigating(false);
    };
    const handleError = (err: any) => {
      console.error('Route change error:', err);
      setIsNavigating(false);
    };

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleError);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleError);
    };
  }, [router.asPath, router.events]);

  // Use the layout defined at the page level, if available
  const getLayout: (page: React.ReactElement) => ReactNode =
    appProps.Component.getLayout ??
    ((page: React.ReactElement) => {
      const isLoginPage = appProps.Component === LoginIndex;
      return (
        <DefaultLayout padding={isLoginPage ? false : true}>{page}</DefaultLayout>
      );
    });  

  function extractToken(query: string) {
    const keyValue = query.split('&').find((pair) => pair.includes('token'));
    return keyValue && keyValue.split('=')[1];
  }

  function extractRedirect(query: string) {
    const keyValue = query.split('&').find((pair) => pair.includes('redirect'));
    return keyValue && keyValue.split('=')[1];
  }

  const [calendar, setCalendar] = useState<any>();
  const [token, setToken] = useState<string | undefined>(getStoredToken());

  const updateCalendar = (newCalendar: any) => {
    setCalendar(newCalendar);
  };

  const storeSicUrl = () => {
    const referrer = document.referrer;
    const origin = window.location.origin;
    if (referrer === '' || referrer.includes(origin)) return;
    localStorage.removeItem('sicUrl');
    localStorage.setItem(
      'sicUrl',
      `${referrer}SICPenaleWEB/SICPenaleServlet?UC=101`
    );
  };

  const redirectExpiredHandler = () => logout(router, '/login');

  useEffect(() => {
    const checkToken = async () => {
      if (!token) {
        const t = extractToken(location.search);
        if (t) {
          try {
            const api = await APIWrapper;
            storeSicUrl();
            const returnedData = await api.call('GET', `auth?token=${t}`);
            storeToken(returnedData);
            const r = extractRedirect(location.search);
            if (r) {
              router.push(r);
            } else {
              router.push('/depositi');
            }
          } catch (error: any) {
            if (error.message === 'Unauthorized') {
              router.push('/login');
            } else {
              throw error;
            }
          }
        } else {
          redirectExpiredHandler();
        }
        router.push('/login');
      }
    };

    checkToken();
  }, [token]);

  return (
    <>
      <CssBaseline />
      <ThemeProvider theme={theme}>
        {i18n.hasLoadedNamespace('translation') && conf ? (
          <NotificationProvider>
            {isNavigating && <NsFullPageSpinner value={1} isOpen={true} />}
            {getLayout(
              <CspClientConfigurationContext.Provider value={conf}>
                <AppInner {...appProps} />
              </CspClientConfigurationContext.Provider>
            )}
          </NotificationProvider>
        ) : (
          <Backdrop
            sx={{
              color: '#fff',
              zIndex: (theme) => theme.zIndex.appBar + 1,
            }}
            open={true}
          >
            <CircularProgress color="inherit" />
          </Backdrop>
        )}
      </ThemeProvider>
    </>
  );
};

const AppInner = ({ Component, pageProps }: AppPropsWithLayout) => {
  const { urlServizi } = useConfig();
  const { environment } = React.useMemo(
    () => initEnvironment({ endpoint: urlServizi }),
    [urlServizi]
  );
  AxiosInteceptor();
  const token = getStoredToken();

  return (
    <RelayEnvironmentProvider environment={environment}>
      {token ? <Component {...pageProps} /> : <Login />}
    </RelayEnvironmentProvider>
  );
};

export default appWithTranslation(MyApp, nextI18nConfig);
