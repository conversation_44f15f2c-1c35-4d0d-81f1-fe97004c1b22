export interface Column {
  id: string;
  label?: string;
  minWidth?: number;
  align?: 'left' | 'right';
  main?: boolean;
  size?: number;
  padding?: 'checkbox' | 'none' | 'normal';
  sortable?: boolean;
  format?: (value: number) => string;
  render?: (cell: any, row?: any, index?: number) => any;
  renderHeadCell?: (cell: any, rows?: any, index?: number) => any;
  handleClick?: (cell: any, row?: any, index?: number) => any;
}

export interface MainModalProps {
  modal: boolean;
  closeModal: any;
  style: object;
  title: string;
  body: JSX.Element;
}

export interface DepositoFilterProps {
  magistrati: any;
  filteredRequestBody: any;
  setDepositanteNC: (depositanteNC: string) => any;
  setFilteredRequestBody: (filteredRequestBody: any) => any;
  closeModal: () => void;
  resetCheckboxFilters?: () => void;
}

export interface DepositiEsportaProps {
  closeModal: () => void;
}

export interface FascicoliFilterProps {
  actionApplyFilter: (
    criteriaParameters: any, 
    validationMessage: string, 
    clearData: boolean
  ) => void;
  isLoading: boolean;
  
}

export interface MenuItemProps {
  value: string;
  label: string;
}

export interface RightSideModalProps {
  openDrawer: boolean;
  closeDrawer: () => void;
  refreshTable?: () => void;
  data: any;
}

export interface UserLoginProps {
  userName: string;
  pwd: string;
}

export interface Esito {
  catId: string;
  nome: string;
  status: string;
  tipo: string;
}

export interface FileBustaProps {
  esiti: Esito[];
  idCatBusta: number;
  origine?: string;
}

export interface mapEsitiAndDiagnosiProps {
  esiti: any[];
  diagnosi: any[];
}

export interface Errors {
  type: any;
  message: any;
}

export interface SidemodalData {
  id: number;
  nrg: string;
  sideBarDetails: any[];
  deposito: any; 
  elaborazioneDeposito: any; 
  contenuti: any[]; 
  data?: string; 
  dataAccettazione?: string; 
  dePlano?: number; 
}

interface LawyerProps {
  fullName: string;
  codiceFiscale: string;
}
export interface PartiProps {
  fullName: string;
  codiceFiscale: string;
  type: string;
  lawyers: LawyerProps[];
}

export interface DetailPartiProps {
  parti?: PartiProps[] | null;
}

export interface ReatiProps {
  displayReati: string;
}
export interface RiunitiProps {
  nrg: string;
}

export interface DetailReatiProps {
  reati?: ReatiProps[] | null;
}
export interface DetailRiunitiProps {
  idRicUdienza: number;
}
export interface RicercaRicorsoSentenza{
        id: number,
        idSezione: number,
        sentenza: number,
        tipoSentenza: string,
        idTipoSentenza: number,
        idRicorsoUdienza: number,
        idEstensore: number,
        nRaccg: number,
        dataMinuta: Date,
        dataPubblicazione: Date,
        depositoTelamatico: number,
        pubblicazioneTelematico: number,
        riunito: boolean
}
export interface RicorsoUdienzaRiunito {
    idRicorsoUdienza: number,
    lastUdie: boolean,
    numChiamata: number,
    principale: boolean,
    udienza: any[],
    nrg: number,
    vSentenza: RicercaRicorsoSentenza,
    ricorso: any
    anno: number,
    numero: number,
}

export interface AccettaModalProps {
  oscuramentoDeskCsp?: boolean;
  origine?: string;
  firma?: (azureToken?: string, isFirmaOtp?: boolean) => void;
  tipoProvvedimento?: string;
  id?: any;
  closeModal: any;
  closeDrawer?: () => void;
  refreshData?: () => void;
  refreshTable?: () => void;
  elaborazioneDeposito: any;
}

export interface FirmaModalProps {
  id?: any;
  closeModal: any;
  closeDrawer?: () => void;
  refreshData?: () => void;
  azureToken?: string;
  isFirmaOtp?: boolean;
}

export interface SceltaOscuramentoModalProps {
  firma?: () => void;
    origine: string;
  elaborazioneDeposito: any;
    oscuramentoDeskCsp: boolean
}

export interface TipoMap {
  [key: string]: string;
}

export interface ObjectKeyValues {
  [key: number | string]: string;
}

export type DatiUdienzaProps = {
  changedSezione: (data: boolean) => void;
  isSezioneSelected: boolean;
  searchedQuery: (data: any) => void;
  listaPresidenti: [];
  selectedSezione: string;
  onSezioneChange: (data: any) => void;
};

export type DatiProvvedimentoProps = {
  disabledFields: boolean;
  listaEstensori: (data: boolean) => void;
  searchedQuery: (data: any) => void;
};

export type CompColleggioUdienzaProps = {
  setIsSezioneSelected: (value: boolean) => void;
  isSezioneSelected: boolean;
  resetQueryParams: boolean;
  onResetQueryParams: () => void;
  selectedSezione: string;
  onSezioneChange: (data: any) => void;
};

export type MonitoraggioDataProps = {
  id: string;
  sezione: string;
  dataUdienza: string;
  tipoUdienza: string;
  collegio: string;
  presidente: string;
  relatore: string;
  estensore: string;
  numeroRicorso: string;
  annoRicorso: string;
  tipoRicorso: string;
  stato: string;
  dataMinuta: string;
  totaleGiorni: string;
  annoSezionale: string;
  numeroSezionale: string;
  idProvvedimento: string;
  idSentenza: string;
};

export type MonitoraggioProps = {
  data: MonitoraggioDataProps[];
  hideFilters: () => void;
  resultMessage: string | null;
  pageInfoServer : PageInfoProps,
  actionApplyPagination: (pageInfo: PageInfoProps) => void;
  actionApplySorting: (property: string) => void;
};

export interface DepositoTableProps {
  filteredRequestBody: any;
  loadDepositi: number;
  setParentLoading?: (isLoading: boolean) => void;
  selectedRows: string[];
  setSelectedRows: (rows: string[]) => void;
}
export interface RicercaRiunitiView {
    annoPadre: number,
    idRicUdienPadre: number,
    idRicUdienza: number,
    nrg: number,
    nrgPadre: number,
    nrgRealePadre: number,
    numeroPadre: number,
    principale: boolean,
    riunito: boolean,
}
export interface DataTableWithPaginationProps {
  rows: Array<any>;
  columns: Array<Column>;
  action?: (row: any) => void;  
  actionChangePage: (pageInfo: PageInfoProps) => void;
  pageInfoServer: PageInfoProps;
  maxHeight?: string;
  addPaginate?: boolean;
  pageSize?: number;
  sorting: boolean;
  createSortHandler: any;
  resultMessage?: string | null;
}

export interface FascicoliTableProps {
  result : any[], 
  pageInfoServer : PageInfoProps,
  actionApplyPagination: (pageInfo: PageInfoProps) => void,
  actionApplySorting: (property: string) => void;
}

export interface OrderProps {
  propertyName: string,
  asc?: boolean;
  nullsLast?: boolean
}

export interface PageInfoProps {
  pageNumber?: number;
  pageSize?: number;
  totPages?: number;
  totRecordsCount?: number;
  order?: OrderProps;
  orderList?: OrderProps[];
}

export interface InputPayloadBackend {
  pageInfo?: PageInfoProps;
  criteriaParameters?: any;
  distinct?: boolean
}
