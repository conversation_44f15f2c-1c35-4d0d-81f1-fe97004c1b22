# Install dependencies only when needed
FROM harbor.netserv.it/portali/node:16-alpine AS builder
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat dos2unix

ENV NEXT_TELEMETRY_DISABLED 1

WORKDIR /app
COPY portale-cassazione-penale/ .
RUN rm -f .env.production && mv .env.test .env.production

RUN yarn install --frozen-lockfile

RUN dos2unix .env*


RUN yarn build

FROM builder AS production

WORKDIR /app
COPY --from=builder /app/.next/standalone/ ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public
COPY portale-cassazione-penale/.env.docker .env

ENV NODE_ENV production

RUN mkdir /opt/sharp && cd /opt/sharp && yarn add sharp
RUN echo "NEXT_SHARP_PATH=/opt/sharp/node_modules/sharp" >> .env
# Add Sharp for image optimization in production
# RUN yarn add sharp --production=true

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown -R nextjs: /app

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
