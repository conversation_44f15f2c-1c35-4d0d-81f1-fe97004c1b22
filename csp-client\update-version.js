// Importare i moduli fs e properties-reader
const fs = require('fs');
const propertiesReader = require('properties-reader');

// Leggere il file gradle.properties e ottenere il valore della variabile version
const gradleProperties = propertiesReader('../gradle.properties');
const version = gradleProperties.get('version');

// Leggere il file package.json e parsarlo come un oggetto JSON
const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));

// Aggiornare la proprietà version dell'oggetto JSON con il valore della variabile version
packageJson.version = version;

// Scrivere l'oggetto JSON modificato nel file package.json
fs.writeFileSync('./package.json', JSON.stringify(packageJson, null, 2));
