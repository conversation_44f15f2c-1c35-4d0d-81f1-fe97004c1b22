import { Grid, Typography } from '@mui/material';
import { NsButton, required, NsTextInput } from '@netservice/astrea-react-ds';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'relay-forms';
import { FormLayout } from 'src/layouts/FormLayout';

function RicercaPage() {
  const { t } = useTranslation();
  const [display, setDisplay] = React.useState<string | null>(null);
  const { submit, reset } = useForm<any>({
    onSubmit: (v: { field1: string; field2: string }) => {
      setDisplay(`Dati: ${v.field1} - ${v.field2}`);
    },
  });

  const clear = useCallback(() => {
    setDisplay(null);
    reset();
  }, [setDisplay, reset]);

  return (
    <Grid container>
      <Grid container item>
        <Grid item>
          <NsTextInput
            name="field1"
            validate={required}
            errorMessage={t('form.errors.field1')!}
            label={t('form.labels.field1')}
          />
        </Grid>
        <Grid item>
          <NsTextInput
            name="field2"
            validate={required}
            errorMessage={t('form.errors.field1')!}
            label={t('form.labels.field2')}
          />
        </Grid>
      </Grid>
      <Grid container item>
        <Grid item>
          <NsButton onClick={submit}>{t('common.submit')}</NsButton>
        </Grid>
        <Grid item>
          <NsButton onClick={clear}>{t('common.reset')}</NsButton>
        </Grid>
      </Grid>
      <Typography>{display}</Typography>
    </Grid>
  );
}

RicercaPage.getLayout = (page: React.ReactElement) => (
  <FormLayout padding={true}>{page}</FormLayout>
);

export default RicercaPage;
