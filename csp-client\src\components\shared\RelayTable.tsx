import {
  Box,
  Grid,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { HeadTableCell, StyledTableCell } from './TableCells';

export default function RelayTable({
  rows,
  columns,
  action,
  maxHeight = 'unset',
  addPaginate = false,
  sorting,
  createSortHandler,
  orderBy,
  order,
  resultMessage = null,
}: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [rowsPerPage, setRowsPerPage] = useState(addPaginate ? 25 : 100);
  const [page, setPage] = useState(0);
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };
  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const baseColumns = columns.filter((column: any) => !column?.main);
  const mainHeaders = columns.filter((column: any) => column?.main);

  return (
    <>
      {rows.length > 0 ? (
        <TableContainer
          sx={{
            border: theme.custom.borders[0],
            borderBottom: 'unset',
            maxHeight,
          }}
          component={Paper}
        >
          <TableContainer>
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow>
                  {mainHeaders.map((column: any, i: number) => {
                    return column.main ? (
                      <HeadTableCell colSpan={column.size} key={i}>
                        {' '}
                        {column.label}{' '}
                      </HeadTableCell>
                    ) : null;
                  })}
                </TableRow>
                {sorting && createSortHandler ? (
                  <TableRow>
                    {baseColumns.map((column: any, i: number) => {
                      return (
                        <StyledTableCell
                          key={i}
                          onClick={() => {
                            if (column.id !== 'diagnosi') {
                              createSortHandler(column.id);
                            }
                          }}
                          sx={{
                            cursor:
                              column.id !== 'diagnosi' ? 'pointer' : 'default',
                          }}
                        >
                          {column.id !== 'diagnosi' ? (
                            <TableSortLabel
                              active={orderBy === column.id}
                              direction={
                                orderBy === column.id &&
                                order !== '' &&
                                order === 'desc'
                                  ? 'desc'
                                  : 'asc'
                              }
                            >
                              {column.renderHeadCell
                                ? column.renderHeadCell(column.label, rows)
                                : column.label}{' '}
                            </TableSortLabel>
                          ) : (
                            <Box>
                              {column.renderHeadCell
                                ? column.renderHeadCell(column.label, rows)
                                : column.label}{' '}
                            </Box>
                          )}
                        </StyledTableCell>
                      );
                    })}
                  </TableRow>
                ) : (
                  <TableRow>
                    {baseColumns.map((column: any, i: number) => {
                      return (
                        <StyledTableCell
                          style={{ minWidth: column.minWidth }}
                          key={i}
                        >
                          {column.renderHeadCell
                            ? column.renderHeadCell(column.label, rows)
                            : column.label}{' '}
                        </StyledTableCell>
                      );
                    })}
                  </TableRow>
                )}
              </TableHead>
              <TableBody>
                {rows
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row: any, i: number) => {
                    const currentId = i + 10 * page;
                    return (
                      <TableRow
                        hover
                        role="checkbox"
                        tabIndex={-1}
                        key={i}
                        sx={action != null ? { cursor: 'pointer' } : null}
                        onClick={
                          action != null ? () => action(row) : () => null
                        }
                        className='ns-custom-tr'
                      >
                        {baseColumns.map((column: any, index: number) => {
                          const value = row[column.id];
                          if (column.render) {
                            return React.cloneElement(
                              column.render(column, row, currentId),
                              { key: `cell-${i}-${index}` }
                            );
                          }
                          return (
                            <TableCell
                              key={`cell-${i}-${index}`}
                              align={column.align}
                              sx={{
                                border: theme.custom.borders[0],
                                background: row?.color ? row.color : '',
                                color: row?.color == 'black' ? 'white' : '',
                              }}
                            >
                              {value}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>
        </TableContainer>
      ) : (
        <Grid width="100%">
          <Typography mt={3} variant="h3">
            {resultMessage || t('fascicoli.nessunRisultato')}
          </Typography>
        </Grid>
      )}
      {addPaginate && (
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Righe per pagina"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} di ${count}`
          }
        />
      )}
    </>
  );
}
