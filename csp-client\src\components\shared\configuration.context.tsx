import React, { useContext } from 'react';

export interface CspClientConfiguration {
    urlServizi?: string;
    signAuthAzure?: string;
    signClientId?: string;
    signRedirect?: string;
    signScope?: string;
    enabledFirmaOtp?: boolean;
}

export const CspClientConfigurationContext =
    React.createContext<CspClientConfiguration>({});

export function useConfig() {
    return useContext(CspClientConfigurationContext);
}
