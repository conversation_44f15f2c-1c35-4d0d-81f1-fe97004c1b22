import React, { useEffect, useMemo, useState } from 'react';
import { Box, Grid, MenuItem, Typography } from '@mui/material';
import { itIT } from '@mui/x-date-pickers';
import {
  NsDateCalendar,
  NsSelectAutocomplete,
  NsTextInput,
  NsTooltip,
} from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import useDebouncedCallback from '@restart/hooks/useDebouncedCallback';
import { DatiProvvedimentoProps, MenuItemProps } from '../interfaces';
import { isAnno, rangeValidatorFactory } from '../shared/Utils';
import APIWrapper from 'src/utils/APIWrapper';

export default function DatiProvvedimento({
  disabledFields,
  listaEstensori,
  searchedQuery,
}: DatiProvvedimentoProps) {
  const dateRangeValidator = (fieldName: string) =>
    rangeValidatorFactory(fieldName);

  const [isNrgDa, setIsNrgDa] = useState(false);
  const [isNrgA, setIsNrgA] = useState(false);
  const [isNrgDa2, setIsNrgDa2] = useState(false);
  const [isNrgA2, setIsNrgA2] = useState(false);

  const [nrgDa, setNrgDa] = useState('');
  const [nrgA, setNrgA] = useState('');
  const [nrgDa2, setNrgDa2] = useState('');
  const [nrgA2, setNrgA2] = useState('');

  const [parte, setParte] = useState<string>('');
  const debounceParti = useDebouncedCallback((parte) => {
    if (parte.length > 1) 
      getCognomiParti(parte);
    }, 500);
      
  const [isParteLoading, setIsParteLoading] = useState<boolean>(false);
  const [partiOptions, setPartiOptions] = useState<MenuItemProps[]>([]);

  useEffect(() => {
    if (!disabledFields) {
      setIsNrgDa(false);
      setIsNrgA(false);
      setIsNrgDa2(false);
      setIsNrgA2(false);
      setNrgDa('');
      setNrgA('');
      setNrgDa2('');
      setNrgA2('');
    }
  }, [disabledFields]);

  useEffect(() => {
    if (isNrgDa && isNrgA && isNrgDa2 && isNrgA2) {
      searchedQuery({
        numeroRicorsoDa: nrgDa,
        numeroRicorsoA: nrgA,
        annoRicorsoDa: nrgDa2,
        annoRicorsoA: nrgA2,
      });
    }
  }, [isNrgDa, isNrgA, isNrgDa2, isNrgA2, nrgDa, nrgA, nrgDa2, nrgA2]);

  useEffect(() => {
    debounceParti(parte);
  }, [parte]);
  
  const getCognomiParti = async (parteSearch: string) => {
    try {
      setIsParteLoading(true);
            
      const api = await APIWrapper;
      const response = await api.call(
        'GET',
        `monitoraggio/listaParti?parte=${encodeURIComponent(parteSearch)}`
      );
      
      if (response && Array.isArray(response)) {
        const partiItems = response.map((item: any) => ({
          value: item,
          label: item
        }));
        setPartiOptions(partiItems);
      }
    } catch (err) {
      console.error('Errore nel recupero dei suggerimenti:', err);
    } finally {
      setIsParteLoading(false);
    }
  };

  const cognomeParteItems = useMemo(
      () =>
        partiOptions.map(({ value, label }) => (
          <MenuItem key={value} value={value}>
            {label}
          </MenuItem>
        )),
      [partiOptions]
    );

  const { t } = useTranslation();
  const italianLocale =
    itIT.components.MuiLocalizationProvider.defaultProps.localeText;
  return (
    <Grid item xs={12}>
      <Box
        border={1}
        borderColor="grey.500"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
        display={'flex'}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('monitoraggio.form.datiProvvedimento')}
          </Typography>
        </legend>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            display: 'flex',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
              flex: 1,
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.numeroRicorso')}
            </Typography>
          </legend>
          <Box
            alignItems="center"
            sx={{
              flexWrap: 'wrap',
              display: 'flex',
              '& > div': {
                width: '100%',
                marginBottom: '10px',
                flex: 1,
              },
            }}
          >
            <NsTextInput
              key="nrgDa"
              label={<Typography>{t('monitoraggio.form.da')}</Typography>}
              name="nrgDa"
              disabled={!disabledFields}
              sx={{ maxWidth: 100 }}
              size="small"
              onChange={(e: any) => {
                if (e.target.value !== '') {
                  setNrgDa(e.target.value);
                  setIsNrgDa(true);
                }
              }}
            />
            <Typography style={{ marginTop: '15px', fontSize: '30px' }}>
              {'/'}
            </Typography>
            <NsTextInput
              key="nrgDa2"
              label={<Typography sx={{ opacity: 0 }}>{'.'}</Typography>}
              name="nrgDa2"
              placeholder="aaaa"
              validate={isAnno}
              errorMessage={'Anno non valido'}
              disabled={!disabledFields}
              sx={{ maxWidth: 60 }}
              size="small"
              onChange={(e: any) => {
                if (e.target.value.length === 4) {
                  setNrgDa2(e.target.value);
                  setNrgA2(e.target.value);
                  setIsNrgDa2(true);
                  setIsNrgA2(true);
                }
              }}
            />
          </Box>
          <Box
            alignItems="center"
            sx={{
              flexWrap: 'wrap',
              display: 'flex',
              '& > div': {
                width: '100%',
                marginBottom: '10px',
                flex: 1,
              },
            }}
          >
            <NsTextInput
              key="nrgA"
              label={<Typography>{t('monitoraggio.form.a')}</Typography>}
              name="nrgA"
              disabled={!disabledFields}
              sx={{ maxWidth: 100 }}
              size="small"
              onChange={(e: any) => {
                if (e.target.value !== '') {
                  setNrgA(e.target.value);
                  setIsNrgA(true);
                }
              }}
            />
            <Typography style={{ marginTop: '15px', fontSize: '30px' }}>
              {'/'}
            </Typography>
            <NsTextInput
              key={`nrgA2-${nrgA2}`}
              label={<Typography sx={{ opacity: 0 }}>{'.'}</Typography>}
              name="nrgA2"
              placeholder="aaaa"
              validate={isAnno}
              errorMessage={'Anno non valido'}
              defaultValue={nrgDa2}
              disabled={true}
              sx={{ maxWidth: 60 }}
              size="small"
             /*  onChange={(e: any) => {
                if (e.target.value.length === 4) {
                  setNrgA2(e.target.value);
                  setIsNrgA2(true);
                }
              }} */
            />
          </Box>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.estensore')}
            </Typography>
          </legend>
          <NsSelectAutocomplete
            key="estensore"
            label={
              <Typography>{t('monitoraggio.form.nomeECognome')}</Typography>
            }
            name="estensore"
            disabled={!disabledFields}
            noOptionsText={t('monitoraggio.form.nessunEstensoreDisponibile')}
            sx={{ minWidth: 200 }}
            size="small"
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: '300px',
                },
              },
            }}
          >
            {listaEstensori &&
              Object.entries(listaEstensori).map(([cf, nome]: any) => (
                <MenuItem key={cf} value={cf}>
                  {nome}
                </MenuItem>
              ))}
          </NsSelectAutocomplete>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Box display="flex">
              <Typography sx={{ fontWeight: 'bold' }}>
                {t('monitoraggio.form.cognomeParte')}
              </Typography>
              <NsTooltip
                title={t('monitoraggio.form.tooltipParte')}
              />
            </Box>
          </legend>

          <NsSelectAutocomplete
              key="cognomeParte"
              name="cognomeParte"
              sx={{ minWidth: 200, marginTop:'30px' }}
              noOptionsText={parte.length > 1 ? t('monitoraggio.form.nessunaParteTrovata') : ''}
              size="small"
              defaultValue={parte}
              filterOptions={(options, state) => options}
              disabled={!disabledFields}
              loading={isParteLoading}
              inputValue={parte}
              onInputChange={(_, newInputValue) => {
                setParte(newInputValue);
              }}
            >
              {cognomeParteItems}
            </NsSelectAutocomplete>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
          display={'flex'}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.dataMinuta')}
            </Typography>
          </legend>
          <Grid
            container
            spacing={4}
            sx={{ display: 'flex', flexWrap: 'wrap', maxWidth: 400 }}
          >
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataMinutaDa"
                label={<Typography>{t('monitoraggio.form.da')}</Typography>}
                name="dataMinutaDa"
                disabled={!disabledFields}
                size="small"
                errorMessage={[
                  t('form.errors.required', {
                    field: 'Data minuta da',
                  }),
                ]}
                onChange={(e: any) =>
                  searchedQuery({ dataMinutaDa: e.target.value })
                }
                inputProps={{ readOnly: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataMinutaA"
                label={<Typography>{t('monitoraggio.form.a')}</Typography>}
                name="dataMinutaA"
                disabled={!disabledFields}
                size="small"
                errorMessage={[t('monitoraggio.form.validazioneDataMinuta')]}
                validate={dateRangeValidator('dataMinutaDa')}
                dependsOn={['dataMinutaDa']}
                onChange={(e: any) =>
                  searchedQuery({ dataMinutaA: e.target.value })
                }
                inputProps={{ readOnly: true }}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
          display={'flex'}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.dataPubblicazione')}
            </Typography>
          </legend>
          <Grid
            container
            spacing={4}
            sx={{ display: 'flex', flexWrap: 'wrap', maxWidth: 400 }}
          >
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataPubblicazioneDa"
                label={<Typography>{t('monitoraggio.form.da')}</Typography>}
                name="dataPubblicazioneDa"
                disabled={!disabledFields}
                size="small"
                errorMessage={[
                  t('form.errors.required', {
                    field: 'Data pubblicazione da',
                  }),
                ]}
                onChange={(e: any) =>
                  searchedQuery({ dataPubblicazioneDa: e.target.value })
                }
                inputProps={{ readOnly: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataPubblicazioneA"
                label={<Typography>{t('monitoraggio.form.a')}</Typography>}
                name="dataPubblicazioneA"
                disabled={!disabledFields}
                size="small"
                errorMessage={[
                  t('monitoraggio.form.validazioneDataPubblicazione'),
                ]}
                validate={dateRangeValidator('dataPubblicazioneDa')}
                dependsOn={['dataPubblicazioneDa']}
                onChange={(e: any) =>
                  searchedQuery({ dataPubblicazioneA: e.target.value })
                }
                inputProps={{ readOnly: true }}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Grid>
  );
}
