# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AggregatePenaleNotifiche {
  count: Float!
  total: Float!
  unread: Float
}

input ArgsDepositoProvvedimentoInput {
  anRuolo: Float
  firmato: String
  numRuolo: Float
  pin: String
  tipologiaProvvedimento: ProvvedimentiTipoEnum!
}

type CivileAnagdifen {
  codiceAvvocato: String
  codiceFiscale: String
  codiceFiscalePrecedente: String
  codiceForo: Int
  cognome: String
  dUff: Int
  dataCassazionista: DateTime
  dataNascita: DateTime
  deceduto: Int
  dtAvv: DateTime
  dtProc: DateTime
  email: String
  fax: String
  foroProvincia: Int
  idAuloRif: Int
  idParam: ID!
  luogoNascita: String
  nazionalita: String
  nome: String
  provicniaNascita: String
}

type CodeDeposito {
  cf: String!
  codeDepositoDto: CodeDepositoDto
  idProvv: String!
}

type CodeDepositoDto {
  dataUdienza: DateTime
  nrg: String
  numOrdine: Float
  tipoProvvedimento: String
}

input CodeDepositoInput {
  """
  codice fiscale utente
  """
  cf: String!

  """
  id provvedimento
  """
  idProvv: String!
}

input CreateNotificheInput {
  """
  Example field (placeholder)
  """
  descrizione: String!

  """
  Example field (placeholder)
  """
  nrg: Int!

  """
  Example field (placeholder)
  """
  tipo: String
}

input CreateProvvLavorazioneInput {
  allegatoOscurato: Boolean = false
  argsProvvedimento: ArgsDepositoProvvedimentoInput!
  idUdienza: Int!
  nrg: Int!
  origine: ProvvedimentiOrigineEnum!
}

input CreateProvvedimentiChangeStatusInput {
  """
  Example field (placeholder)
  """
  idAutore: Int!

  """
  Example field (placeholder)
  """
  idProvvedimento: String

  """
  Example field (placeholder)
  """
  prevStato: ProvvedimentiStatoEnum

  """
  Example field (placeholder)
  """
  stato: ProvvedimentiStatoEnum!
}

input CreateProvvedimentiInput {
  """
  Example field (placeholder)
  """
  dataDeposito: DateTime!

  """
  Example field (placeholder)
  """
  idUdienza: Int!

  """
  Example field (placeholder)
  """
  nomeDocumento: String!

  """
  Example field (placeholder)
  """
  nrg: Int!

  """
  Example field (placeholder)
  """
  origine: ProvvedimentiOrigineEnum

  """
  Example field (placeholder)
  """
  stato: ProvvedimentiStatoEnum

  """
  Example field (placeholder)
  """
  tipo: ProvvedimentiTipoEnum!
}

input CreateProvvedimentiNoteInput {
  """
  Example field (placeholder)
  """
  idAutore: Int!

  """
  Example field (placeholder)
  """
  idProvvedimento: String!

  """
  Example field (placeholder)
  """
  note: String!
}

input CredenzialiFirmaRemotaInput {
  passwordFirma: String
  pinFirma: String
  usernameFirma: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime
scalar Long

input DatiBustaInput {
  codiceFiscaleMittente: String
  codiceUfficioDestinatario: String
  idMsg: String
  ruoloMittente: String
}

"""
A simple UUID parser
"""
scalar Decimal

input FirmaProvvLavorazioneInput {
  bustaMakerData: DatiBustaInput
  codiceUfficio: String
  credenzialiFirmaRemota: CredenzialiFirmaRemotaInput
  firmato: Boolean!
  generazioneDatiAtto: GenerazioneDatiAttoInput
  idProvvedimento: String!
  nrg: Int!
  tipologiaProvvedimento: ProvvedimentiTipoEnum
}

input GenerazioneDatiAttoInput {
  allegatoOscurato: String = "false"
  annoFascicolo: Int
  numeroFascicolo: Int
  tipoProvvedimento: String
}

type Mutation {
  GenerazioneProvvedimentoCreateMutation(
    createProvvLavorazioneInput: CreateProvvLavorazioneInput!
  ): PenaleProvvedimenti!
  creaCodeDeposito(codaDeposito: CodeDepositoInput!): CodeDeposito!
  creaNotifica(notifica: CreateNotificheInput!): PenaleNotifiche!
  createProvvedimento(
    provvedimento: CreateProvvedimentiInput!
  ): PenaleProvvedimenti!
  createProvvedimentoChangeStatus(
    changeStatus: CreateProvvedimentiChangeStatusInput!
  ): PenaleProvvChangeStatus!
  createProvvedimentoNote(
    provvedimento: CreateProvvedimentiNoteInput!
  ): PenaleProvvedimentiNote!
  eliminaCodeDeposito(idProvv: String!): Boolean!
  firmaEDeposita(
    firmaProvvLavorazioneInput: FirmaProvvLavorazioneInput!
  ): PenaleProvvedimenti!
  unNotificaToRead(id: String!): PenaleNotifiche!
  updateReadAllNotify: Boolean!
  updateReadNotifyList(segnaLette: NotificheToReadInput!): Boolean!
}

input NotificheToReadInput {
  ids: [String!]!
}

type MockPerson {
  id: ID
  firstName: String
  lastName: String
  birthDate: Long
}

type PageInfo {
  endCursor: String
  hasNextPage: Boolean
  hasPreviousPage: Boolean
  startCursor: String
}

type PenaleAnagmagis {
  codiceFiscale: String
  cognome: String
  dataNascita: DateTime
  idAnagmagis: ID!
  idFunzione: Int
  nome: String
  oggi: DateTime
  operatore: Int
}

type PenaleAnagmagisDetails {
  codiceFiscale: String
  cognome: String
  dataNascita: DateTime
  idAnagmagis: ID!
  idFunzione: Int
  nome: String
  oggi: DateTime
  operatore: Int
}

type PenaleAnagraficaParti {
  codiceFiscale: String
  codiceUnivoco: String
  cognome: String!
  dataNascita: DateTime
  glbTime: Int
  idAnagParte: ID!
  idFunzione: Int
  luogoNascita: String
  nazionalita: Int
  nome: String
  oggi: DateTime
  operatore: Int
  provinciaNascita: String
}

type PenaleColleggioDetails {
  colleggioMagistrati: [PenaleCollegioDetailsModel!]
  relatore: PenaleTMagisDeatils
}

type PenaleCollegio {
  glbDtime: Int
  gradoMag: Int!
  idFunzione: ID!
  idMagis: Int!
  idMagiscolle: Int!
  idUdienza: Int!
  inUdienza: Int
  isRelatore: Boolean
  magistrato: PenaleTMagis!
  oggi: DateTime!
  operatore: Int!
  tipoMag: String!
}

type PenaleCollegioDetailsModel {
  glbDtime: Int
  gradoMag: Int!
  idFunzione: ID!
  idMagis: Int!
  idMagiscolle: Int!
  idUdienza: Int!
  inUdienza: Int
  isRelatore: Boolean
  magistrato: PenaleTMagisDeatils
  oggi: DateTime!
  operatore: Int!
  tipoMag: String!
}

type PenaleDifenparti {
  avviso: String
  cap: String
  comune: String
  dataAnomina: DateTime
  dataRevoca: DateTime
  dataRinunzia: DateTime
  deceduto: Int
  difensoreAnagrafica: CivileAnagdifen
  esito: String
  gruppo: Int
  idAnagraficaDifensore: Int!
  idDifensoriParti: ID!
  idFunzione: Int!
  idParti: Int!
  indirizzo: String
  merito: String
  nrg: Int!
  oggi: DateTime
  operatore: Int!
  parte: PenaleTParti
  provincia: String
  tipoDifensore: PenaleParam
}

type PenaleListPartiEControParteModel {
  controParte: [PenaleTParti!]!
  parte: PenaleTParti!
}

type PenaleListPartiModel {
  controParteList: [PenaleListPartiEControParteModel!]!
  parte: [PenaleTParti!]!
}

type PenaleNotifiche {
  annoFascicolo: Float
  coleggio: PenaleParam
  dataCreazione: DateTime
  dataUdienza: DateTime
  descrizione: String
  fineUdienza: DateTime
  idNotifica: ID!
  idUdienza: Float
  idUtente: Int!
  inizioUdienza: DateTime
  nrg: Int!
  numeroFascicolo: Float
  read: Boolean
  sezione: PenaleParam
  tipo: String
  tipoProvvedimento: ProvvedimentiTipoEnum
  tipoUdienza: PenaleParam
}

type PenaleNotificheConnection {
  aggregate: AggregatePenaleNotifiche!
  edges: [PenaleNotificheEdge!]!
  idUdienza: Int
  pageInfo: PageInfo!
}

type PenaleNotificheEdge {
  """
  Used in `before` and `after` args
  """
  cursor: String!

  """
  The target node
  """
  node: PenaleNotifiche!
}

type PenaleParam {
  descrizione: String!
  idParam: ID!
  sigla: String!
}

type PenaleParteLegate {
  anagraficaParte: PenaleAnagraficaParti
  idAnagraficaParte: ID!
  idFunzione: Int!
  idParte: ID!
  operatore: Int!
  tipoLegame: PenaleParam
}

type PenaleProfilo {
  dataFine: DateTime
  dataInizio: DateTime
  descrizioneProfilo: String
  idProfilo: ID!
  inUsoA: String
  operatore: Int
}

type PenaleProvvChangeStatus {
  dateChange: DateTime!
  idAutore: Int!
  idProvvedimento: String!
  idProvvedimentoChangeStatus: ID!
  prevStato: String
  stato: String!
}

type PenaleProvvLavFile {
  createAt: DateTime!
  idCategoria: ID!
  idProvvedimento: String!
  mimeType: String!
  nomeFile: String!
  oscurato: Boolean!
  signed: Boolean!
  tipoFile: String!
}

type PenaleProvvedimenti {
  autore: PenaleTUtente
  checkDownloadAndSign: Boolean
  dataDecisione: DateTime!
  dataDeposito: DateTime
  dataUltimaModifica: DateTime!
  disabledButton: Boolean
  disabledCreaNuovoProv: Boolean
  disabledModificaOrDuplica: Boolean
  enabledRichiestaDiModificaEVerificato: Boolean
  fkIdCat: String
  idAutore: Int!
  idProvvedimento: ID!
  idUdienza: Int!
  isDuplicato: Boolean
  listaFile: [PenaleProvvLavFile!]
  nomeDocumento: String
  note: [PenaleProvvedimentiNote!]
  nrg: Int!
  origine: String
  stato: String
  tipo: String
}

type PenaleProvvedimentiNote {
  autore: PenaleTUtente
  dataInserimento: DateTime!
  idAutore: Int!
  idProvvNote: ID!
  idProvvedimento: String!
  note: String!
}

type PenaleReatiRicorso {
  dataA: DateTime
  dataDa: DateTime
  glbDtime: Int
  idFunzione: Int!
  idReatiRicorsi: ID!
  idReato: Int
  istProc: Int!
  note: String
  nrg: Int!
  oggi: DateTime!
  operatore: Int!
  principale: Boolean!
  reato: PenaleTReati
  ricorso: PenaleTRicorso
  tipoD: PenaleParam
  tipoData: String
}

type PenaleTEsito {
  art28: String!
  art94: String!
  idEsito: Int!
  idFunzione: ID!
  idReatoParte: String!
  idRicUdienza: Int!
  motivoSosp: String!
  note: String!
  operatore: Int!
  privacy: String!
  rinvioDescrizione: String!
  riunito: String!
  semplificata: String!
}

type PenaleTMagis {
  anagraficaMagistrato: PenaleAnagmagis
  codMag: Int
  dataFine: DateTime
  dataInizio: DateTime!
  glbDtime: Int
  grado: Int!
  idAnagmagis: Int
  idFunzione: Int
  idMagis: ID!
  oggi: DateTime
  operatore: Int
  tipoMag: PenaleParam
  ufficio: Int!
}

type PenaleTMagisDeatils {
  anagraficaMagistrato: PenaleAnagmagisDetails
  codMag: Int
  dataFine: DateTime
  dataInizio: DateTime!
  glbDtime: Int
  grado: Int!
  idAnagmagis: Int
  idFunzione: Int
  idMagis: ID!
  oggi: DateTime
  operatore: Int
  tipoMag: PenaleParam
  ufficio: Int!
}

type PenaleTParti {
  altri: String
  anagraficaParte: PenaleAnagraficaParti
  art159: String
  art161: String
  art165: String
  dataDecor: DateTime
  dataScarcerazione: DateTime
  datarresto: DateTime
  difensori: [PenaleDifenparti!]!
  displayParti: String
  dtinizStato: DateTime!
  glbDtime: Int
  idAnagraficaParte: Int
  idFunzione: Int!
  idParamfig: Int
  idParte: ID!
  idTipoFig: Int!
  nrg: Int!
  numOrdine: Int
  oggi: DateTime!
  operatore: Int!
  parteLegata: PenaleParteLegate
  penaSup5: String!
  privacy: Int
  ricorrente: Boolean!
  secretata: String
  statoParte: String
  stralcio: String
  tipoFig: PenaleParam
  tipoLegame: Int
  uffins: Int
}

type PenaleTProvved {
  dataProvv: DateTime!
  gradoProvv: PenaleParam!
  idProvvedimento: ID!
  impugnato: Boolean!
  nrg: Int!
  numProvv: Int!
  oggi: DateTime!
  operatore: Int!
  tipoProvv: PenaleParam!
}

type PenaleTReati {
  aggrava: String!
  altroIdentificativo: Int
  anno: Int!
  art: Int!
  capo: Int!
  comma: Int!
  dataFineUtilizzo: DateTime
  descrizione: String!
  displayReati: String
  eppo: String
  fonteNorm: String!
  glbDtime: Int
  gruppo: Int
  idFunzione: Int!
  idReato: ID!
  idVoce: Int!
  lettera: String!
  libro: Int!
  numeroLegale: Int!
  oggi: DateTime!
  operatore: Int!
  paragrafo: Int!
  privacy: Int
  provenienza: String
  titolo: Int!
  valido: String
}

type PenaleTRicorso {
  anno: Int!
  codinam1: Int
  codinam2: Int
  codinam3: Int
  confisca: String!
  dataDecorsoImp: DateTime
  dataIscrizione: DateTime!
  dataPassa: DateTime
  dataPassaFto: DateTime
  dataPassaInc: DateTime
  detParti: String
  doveFto: PenaleParam
  doveSta: PenaleParam
  flagSitmp: String!
  glbDtime: Int
  idAuloInc: Int
  idDoveinc: Int
  idFunzionario: Int!
  idMagisinam: Int
  idRinvio: Int
  idSezerr: Int
  listaParti: PenaleListPartiModel
  mafia: String!
  note: String
  nrg: ID!
  nrgPrinc: Int
  nrgReale: Int!
  numParti: Int
  numero: Int!
  oggi: DateTime!
  operatore: Int!
  parti: [PenaleTParti!]
  privacy: Int
  provvedimentoImpugnato: String
  reatiRicorso: [PenaleReatiRicorso!]
  sezione: PenaleParam
  spoglio: PenaleTSpoglio
  statoRicorso: String
  tipoRicorso: PenaleParam
  tipoRicorsoOld: String
  urgente: String
  vecchioRito: String!
}

type PenaleTRicudien {
  anno: Int
  art161: String!
  art169: String!
  oscuramentoSic: Boolean
  daOscurato: Boolean
  esito: PenaleTEsito!
  faxEsito: String!
  glbDTime: Int!
  idConcl1: Int!
  idConcl2: Int!
  idConcl3: Int!
  idEsito: Int!
  idFunzione: Int!
  idMagiscolle: Int!
  idMotivorinv: Int!
  idRelatore: Int!
  idRicudien: ID!
  idSosp: Int!
  idUdienza: Int!
  importante: String!
  notiPres: String!
  nrg: Int!
  numOrdine: Int!
  numero: Int
  oggi: DateTime!
  operatore: Int!
  principale: Boolean!
  provvedimento: PenaleProvvedimenti
  relatore: PenaleTMagis
  ricorso: PenaleTRicorso
  tipologia: String
  urgente: String!
}

type PenaleTSpoglio {
  art12: String
  art127: String!
  art438: String!
  art444: String!
  art611: String!
  codiceConf: Int
  cognomePmcc: String
  cognomePmce: String
  commento: String
  dataDecreto: DateTime
  dataDecterm: DateTime
  dataPassaPg: DateTime
  dataPrescriz2: DateTime
  dataPrescrizione: DateTime
  dataSosp: DateTime
  deplano: String
  glbDtime: Int
  idAuloPmcc: Int
  idAuloPmce: Int
  idFunzione: Int
  idMotivo: Int
  idSpogliatore: Int
  idSpoglio: ID!
  idTipoudPrev: Int!
  mae: String
  modello: Int
  nomePmcc: String
  nomePmce: String
  note: String
  nrg: Int!
  oggi: DateTime
  operatore: Int
  pgCustodia: Int
  pgSuper: Int
  presidi: String
  privacy: Int
  urgentePg: String
  valPond: Decimal
  vecchiRito: String
}

type PenaleTUdienza {
  aula: PenaleParam
  collegio: [PenaleCollegio!]!
  dataUdienza: DateTime!
  fineUdienza: DateTime
  idFunzionario: Int!
  idTipoDiUdienza: Int!
  idUdien: ID!
  inizioUdienza: DateTime
  notePg: String
  oggi: DateTime!
  operatore: Int!
  ricorsiUdienza: [PenaleTRicudien!]
  sezione: PenaleParam!
  termineDeposito: DateTime!
  termineDepositoCalendar: DateTime
  tipoUdienza: PenaleParam!
}

type PenaleTUdienzaEntityFake {
  aula: String!
  dataDeposito: DateTime!
  dataScadenza: DateTime!
  dataUdienza: DateTime!
  idProvvvedimento: String!
  idTipoDiUdienza: Int!
  idUdien: ID!
  nrg: Int!
  nrgReale: Int!
  numero: Int!
  numeroChiamata: Int!
  sezione: String!
  statoProvvedimento: String!
  statoProvvedimentoLavorazione: String!
}

type PenaleTUtente {
  codiceFiscale: String
  cognome: String!
  idProfilo: Int
  idUtente: ID!
  identificativo: String!
  nome: String
  operatore: Int!
  profilo: PenaleProfilo
  tipoUtente: String
  tipologia: PenaleParam
  uffAppartenenza: PenaleParam
  ufficio: PenaleParam
}

type PenaleUdienza {
  aula: String
  dataUdienza: DateTime!
  fineUdienza: DateTime
  idFunzione: Int!
  idUdienza: ID!
  inizioUdienza: DateTime
  notePg: String
  oggi: DateTime!
  operatore: Int!
  sezione: String!
  tipoUdienza: String!
}

enum ProvvedimentiOrigineEnum {
  LOCALE
  SYSTEM
}

enum ProvvedimentiStatoEnum {
  BUSTA_RIFIUTATA
  CODA_DI_FIRMA
  DUPLICATA
  ERRORE_DI_PUBBLICAZIONE
  INVIATO_IN_CANCELLERIA_PRESIDENTE
  INVIATO_IN_CANCELLERIA_RELATORE
  IN_BOZZA
  MINUTA_ACCETTATA
  MINUTA_DA_MODIFICARE
  MINUTA_MODIFICATA
  PUBBLICATA
}

enum ProvvedimentiTipoEnum {
  MINUTA_ORDINANZA
  MINUTA_SENTENZA
  ORDINANZA
  SENTENZA
}

type Query {
  anagraficaDifensore(id: Float!): CivileAnagdifen!
  anagraficaMagistrati: [PenaleAnagmagis!]!
  anagraficaMagistrato(id: Float!): PenaleAnagmagis!
  anagraficaParte(id: Float!): PenaleAnagraficaParti!
  anagraficaParti: [PenaleAnagraficaParti!]!
  codeDeposito: [CodeDeposito!]!
  codeDepositoByCf: [CodeDeposito!]!
  codeDepositoByIdProvv(idProvv: String!): CodeDeposito!
  codeDepositoByIdProvvCf(idProvv: String!): CodeDeposito!
  colleggioDetails(id: Float!, nrg: Float!): PenaleColleggioDetails!
  colleggioDetailsByOrdine(id: Float!, ordine: Float!): PenaleColleggioDetails!
  collegi: [PenaleCollegio!]!
  collegio(id: Float!): PenaleCollegio!
  collegioByNrgAndIdUdienza(id: Float!, nrg: Float!): [PenaleCollegio!]!
  countNotificheNotRead: Int!
  difensoreParti(id: Float!): PenaleDifenparti!
  difensoriParti: [PenaleDifenparti!]!
  getProvvLavorazione(nrg: Float!): PenaleProvvedimenti!
  getProvvLavorazioneByIdProvv(idProvv: String!): PenaleProvvedimenti!
  magistrati: [PenaleTMagis!]!
  magistrato(id: Float!): PenaleTMagis!
  notifica(id: String!): PenaleNotifiche!
  notifiche(
    after: String
    before: String
    first: Int
    last: Int
    term: String
  ): PenaleNotificheConnection!
  notificheByCurrentUser(
    after: String
    before: String
    first: Int
    last: Int
    term: String
  ): PenaleNotificheConnection!
  parte(id: Float!): PenaleTParti!
  parteLegate(id: Float!): PenaleParteLegate!
  parti: [PenaleTParti!]!
  partiLegate: [PenaleParteLegate!]!
  penaleUdienzaByIdUdienza(idUdienza: Float!): PenaleUdienza!
  provvedimenti: [PenaleProvvedimenti!]!
  provvedimentiImpugnati: [PenaleTProvved!]!
  provvedimentiNote: [PenaleProvvedimentiNote!]!
  provvedimento(id: String!): PenaleProvvedimenti!
  provvedimentoByIdUdien(id: Float!): [PenaleProvvedimenti!]!
  provvedimentoByIdUdienzaAndOrdine(
    id: Float!
    ordine: Float!
  ): [PenaleProvvedimenti!]!
  provvedimentoByNrg(nrg: Float!): [PenaleProvvedimenti!]!
  provvedimentoByNrgPerPresidente(nrg: Float!): [PenaleProvvedimenti!]!
  provvedimentoChangeStatus: [PenaleProvvChangeStatus!]!
  provvedimentoChangeStatusById(id: String!): PenaleProvvChangeStatus!
  provvedimentoChangeStatusByIdProvvedimento(
    id: String!
  ): [PenaleProvvChangeStatus!]!
  provvedimentoImpugnato(id: Float!): PenaleTProvved!
  provvedimentoNote(id: String!): [PenaleProvvedimentiNote!]!
  provvedimentoTrackingByIdProvvedimento(
    id: String!
  ): [PenaleProvvChangeStatus!]!
  reati: [PenaleTReati!]!
  reatiRicorso: [PenaleReatiRicorso!]!
  reato(id: Float!): PenaleTReati!
  reatoRicorso(id: Float!): PenaleReatiRicorso!
  ricorsi: [PenaleTRicorso!]!
  ricorsiUdienza: [PenaleTRicorso!]!
  ricorso(id: Float!): PenaleTRicorso!
  ricorsoUdienza(id: Float!): PenaleTRicudien!
  spogli: [PenaleTSpoglio!]!
  spoglio(id: Float!): PenaleTSpoglio!
  termineDepositoCalendar(
    endDate: DateTime!
    startDate: DateTime!
  ): [PenaleTUdienzaEntityFake!]!
  udienza(id: Float!): PenaleTUdienza!
  udienzaPerPresidente(id: Float!): PenaleTUdienza!
  udienze: [PenaleTUdienza!]!
  utenteById(id: Float!): PenaleTUtente!
  utenti: [PenaleTUtente!]!
  utentiByCf(cf: String!): PenaleTUtente!
  getMockPeople: [MockPerson!]
}
