import { utentiQuery } from '@/generated/utentiQuery.graphql';
import { STORE_OR_NETWORK, graphql, useQuery } from 'relay-hooks';
import { UtentiComponent } from 'src/components/UtentiComponent';
import { withWait } from 'src/components/relay/waitQueryResult';

const anagQuery = graphql`
  query utentiQuery {
    ...UtentiComponentFragment
  }
`;

export default function AnagraficaUtentiPage() {
  const data = useQuery<utentiQuery>(
    anagQuery,
    {},
    { fetchPolicy: STORE_OR_NETWORK }
  );
  const Wrapped = withWait(UtentiComponent, data);
  return <Wrapped />;
}
