import React, { useEffect, useMemo, useRef } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  ColumnSizingState,
} from '@tanstack/react-table';
import {
  keepPreviousData,
  useInfiniteQuery,
  InfiniteData,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
  alpha,
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableContainerProps,
  TableHead,
  TableRow,
  Typography,
  CircularProgress,
} from '@mui/material';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ImportExportIcon from '@mui/icons-material/ImportExport';
import { NsTooltip } from '@netservice/astrea-react-ds';

/**
 * Options for fetching data with pagination, sorting, and filtering
 * @template TData - The type of data being fetched
 */
export interface FetchDataOptions<TData> {
  pageParam?: number;
  fetchSize: number;
  sorting?: SortingState;
  filters?: Record<string, unknown>;
}

/**
 * Response format for API data with pagination metadata
 * @template T - The type of data in the response
 */
export interface ApiResponse<TData> {
  data: TData[];
  meta: {
    totalRowCount: number;
  };
}

/**
 * Options for Relay pagination
 */
export interface RelayLoadMoreOptions {
  pageParam: number;
  fetchSize: number;
}

// Base props shared between both modes
interface BaseDataGridProps<TData extends object>
  extends Omit<TableContainerProps, 'theme'> {
  columns: ColumnDef<TData>[];
  initialSorting?: SortingState;
  containerHeight?: string | number;
  estimatedRowHeight?: number;
  debug?: boolean;
  enableColumnGrouping?: boolean;
}

// Props specific to React Query mode
interface ReactQueryModeProps<TData extends object> {
  mode?: 'react-query';
  fetchData: (options: FetchDataOptions<TData>) => Promise<ApiResponse<TData>>;
  fetchSize?: number;
  queryClient?: QueryClient;
  filteredRequestBody?: any;
  data?: never;
  totalRowCount?: never;
  onLoadMore?: never;
  isLoading?: never;
  hasMore?: never;
}

// Props specific to Relay mode
interface RelayModeProps<TData extends object> {
  mode: 'relay';
  data: readonly TData[] | TData[];
  totalRowCount: number;
  onLoadMore: (options: RelayLoadMoreOptions) => void;
  isLoading?: boolean;
  hasMore?: boolean;
  fetchData?: never;
  fetchSize?: number;
  queryClient?: never;
}

export type NsDataGridVirtualizedInfiniteScrollingProps<TData extends object> =
  BaseDataGridProps<TData> &
    (ReactQueryModeProps<TData> | RelayModeProps<TData>);

const TABLE_HEADER_COLOR = 'rgba(230, 230, 230)';
const TABLE_GROUP_HEADER_L1_COLOR = 'rgba(178, 178, 178)';
const TABLE_GROUP_HEADER_L2_COLOR = 'rgba(200, 200, 200)';
const BORDER_PIXEL = 1;

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  overflow: 'auto',
  '& table': {
    borderCollapse: 'separate',
    borderSpacing: 0,
    tableLayout: 'fixed',
    width: '100%',
  },
  '& thead tr': { display: 'flex' },
  '& th, & td': {
    margin: 0,
    padding: '0.5rem',
    position: 'relative',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    backgroundColor: theme.palette.background.paper,
    boxSizing: 'border-box',
    fontSize: '0.875rem',
    '&:not(:last-child)': {
      borderRight: theme.custom.borders[0],
    },
  },
  '& th': {
    position: 'relative',
    fontWeight: 'bold',
    textAlign: 'left',
    backgroundColor: TABLE_HEADER_COLOR,
    '&:hover .resizer': {
      opacity: 1,
    },
    '& .resizer': {
      position: 'absolute',
      right: 0,
      top: 0,
      height: '100%',
      width: '4px',
      cursor: 'col-resize',
      userSelect: 'none',
      touchAction: 'none',
      opacity: 0,
      transition: 'opacity 0.2s ease-in-out',
      zIndex: 1,
      '&:hover, &.isResizing': {
        opacity: 1,
        backgroundColor: theme.palette.primary.main,
      },
    },
  },
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  position: 'sticky',
  top: 0,
  zIndex: 1,
  backgroundColor: TABLE_HEADER_COLOR,
  border: theme.custom.borders[0],
  '& th': {
    fontWeight: 600,
    textAlign: 'left',
    padding: '0.75rem 0.5rem',
    position: 'relative',
    '& .MuiTypography-h3': {
      margin: 0,
      fontSize: '0.875rem',
      fontWeight: 600,
    },
    '& .MuiBox-root': {
      '& .MuiSvgIcon-root': {
        color: theme.palette.primary.main,
        transition: 'color 0.2s ease-in-out',
        fontSize: '1rem',
        '&:hover': {
          color: theme.palette.primary.dark,
        },
      },
    },
  },
}));

export function NsDataGridVirtualizedInfiniteScrolling<TData extends object>({
  columns,
  initialSorting = [],
  containerHeight = '600px',
  estimatedRowHeight = 35,
  debug = false,
  enableColumnGrouping = false,
  mode = 'react-query',
  ...props
}: Readonly<NsDataGridVirtualizedInfiniteScrollingProps<TData>>) {
  // Create default query client if using react-query mode
  const defaultQueryClient = useMemo(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            retry: 1,
          },
        },
      }),
    []
  );

  // If in react-query mode, render with QueryClientProvider
  if (mode !== 'relay') {
    const {
      fetchData,
      fetchSize = 50,
      queryClient,
    } = props as ReactQueryModeProps<TData>;

    return (
      <QueryClientProvider client={queryClient || defaultQueryClient}>
        <DataGridContentReactQuery
          columns={columns}
          initialSorting={initialSorting}
          containerHeight={containerHeight}
          estimatedRowHeight={estimatedRowHeight}
          debug={debug}
          enableColumnGrouping={enableColumnGrouping}
          fetchData={fetchData}
          fetchSize={fetchSize}
          {...props}
        />
      </QueryClientProvider>
    );
  }

  // If in relay mode, render directly
  const {
    data,
    totalRowCount,
    onLoadMore,
    isLoading = false,
    hasMore = true,
    fetchSize = 50,
  } = props as RelayModeProps<TData>;

  return (
    <DataGridContentRelay
      columns={columns}
      initialSorting={initialSorting}
      containerHeight={containerHeight}
      estimatedRowHeight={estimatedRowHeight}
      debug={debug}
      enableColumnGrouping={enableColumnGrouping}
      data={data}
      totalRowCount={totalRowCount}
      onLoadMore={onLoadMore}
      isLoading={isLoading}
      hasMore={hasMore}
      fetchSize={fetchSize}
      {...props}
    />
  );
}

// Helper function to determine appropriate tooltip content
function getTooltipContent(header: any) {
  const headerContent = header.column.columnDef.header;

  if (header.id === 'select' || header.column.id === 'select') {
    return 'Seleziona tutti';
  }

  if (typeof headerContent === 'function') {
    // Try to extract the column name or use a default
    return (
      header.column.columnDef.header?.name || header.column.id || 'Colonna'
    );
  }
  return headerContent;
}

// Internal component that handles both modes
function DataGridContentRelay<TData extends object>({
  columns,
  initialSorting,
  containerHeight,
  estimatedRowHeight,
  debug,
  enableColumnGrouping,
  ...props
}: any) {
  const mode = 'relay';
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [sorting, setSorting] = React.useState<SortingState>(initialSorting);
  const [columnSizing, setColumnSizing] = React.useState<ColumnSizingState>(
    () =>
      columns.reduce(
        (acc: any, column: { id: string; size: any }) => ({
          ...acc,
          [column.id as string]:
            typeof column.size === 'number' ? column.size : 150,
        }),
        {}
      )
  );

  // Update column sizing when columns change
  useEffect(() => {
    setColumnSizing((prev) =>
      columns.reduce(
        (acc: any, column: { id: string; size: any }) => ({
          ...acc,
          [column.id as string]:
            prev[column.id as string] ?? column.size ?? 150,
        }),
        {}
      )
    );
  }, [columns]);

    const {
      data,
      totalRowCount,
      onLoadMore,
      isLoading = false,
      hasMore = true,
      fetchSize,
    } = props;
    const isLoadingMore = useRef(false);
    const hasInitiatedScroll = useRef(false);
    const [currentPage, setCurrentPage] = React.useState(0);

    // Make a mutable copy of the data if it's readonly
    const mutableData = useMemo(() => {
      return Array.isArray(data) ? [...data] : [];
    }, [data]);

    // Setup the table with TanStack Table
    const table = useReactTable({
      data: mutableData,
      columns,
      state: {
        sorting,
        columnSizing,
      },
      enableColumnResizing: true,
      columnResizeMode: 'onEnd',
      onColumnSizingChange: setColumnSizing,
      onSortingChange: setSorting,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      manualSorting: true,
      debugTable: debug,
      defaultColumn: {
        minSize: 50,
        maxSize: 1000,
        size: 150,
      },
    });

    const totalSize = useMemo(() => table.getTotalSize(), [table]);

    // Configure row virtualization
    const rowVirtualizer = useVirtualizer({
      count: table.getRowModel().rows.length,
      estimateSize: () => estimatedRowHeight,
      getScrollElement: () => tableContainerRef.current,
      overscan: 5,
    });

    // Function to fetch more data when scroll reaches bottom
    const fetchMoreOnBottomReached = React.useCallback(() => {
      const containerRefElement = tableContainerRef.current;
      if (!containerRefElement) return;

      const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // Validate that there's actual content to scroll and we're not at the first render
      const canFetchMore =
        hasInitiatedScroll.current &&
        scrollHeight > clientHeight &&
        !isLoadingMore.current &&
        hasMore &&
        !isLoading &&
        mutableData.length < totalRowCount;

      if (distanceFromBottom < 500 && canFetchMore) {
        const nextPage = currentPage + 1;

        isLoadingMore.current = true;
        setCurrentPage(nextPage);

        // Call the onLoadMore callback
        onLoadMore({
          pageParam: nextPage,
          fetchSize,
        });

        // Reset loading flag after a delay to prevent multiple calls
        setTimeout(() => {
          isLoadingMore.current = false;
        }, 300);
      }
    }, [
      currentPage,
      hasMore,
      isLoading,
      mutableData.length,
      onLoadMore,
      totalRowCount,
      fetchSize,
    ]);

    // Add scroll event listener to detect when to load more data
    useEffect(() => {
      const element = tableContainerRef.current;
      if (!element) return;

      const handleScroll = () => {
        hasInitiatedScroll.current = true;
        fetchMoreOnBottomReached();
      };

      element.addEventListener('scroll', handleScroll);
      return () => element.removeEventListener('scroll', handleScroll);
    }, [fetchMoreOnBottomReached]);

    // Check if we need to load initial data to fill the viewport
    useEffect(() => {
      if (!isLoading && hasMore && mutableData.length === 0) {
        // Initial load
        onLoadMore({
          pageParam: 0,
          fetchSize,
        });
      }
    }, [hasMore, isLoading, mutableData.length, onLoadMore, fetchSize]);

    // Check if we need to load more data after initial data is loaded
    useEffect(() => {
      const containerRefElement = tableContainerRef.current;
      if (!containerRefElement) return;

      // Initial content doesn't fill the container - load more
      const { scrollHeight, clientHeight } = containerRefElement;
      if (
        scrollHeight <= clientHeight &&
        clientHeight > 0 &&
        !isLoading &&
        hasMore &&
        !isLoadingMore.current &&
        mutableData.length > 0 &&
        mutableData.length < totalRowCount
      ) {
        fetchMoreOnBottomReached();
      }
    }, [
      fetchMoreOnBottomReached,
      hasMore,
      isLoading,
      mutableData.length,
      totalRowCount,
    ]);

    // Reset page counter when sorting changes
    useEffect(() => {
      if (tableContainerRef.current) {
        tableContainerRef.current.scrollTop = 0;
      }
      setCurrentPage(0);
      hasInitiatedScroll.current = false;
    }, [sorting]);

    // Show loading state when no data is available
    if (isLoading && mutableData.length === 0) {
      return (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height={containerHeight}
        >
          <CircularProgress />
        </Box>
      );
    }

    return renderGridContent({
      tableContainerRef,
      table,
      totalSize,
      rowVirtualizer,
      dataLength: mutableData.length,
      totalRowCount,
      estimatedRowHeight,
      containerHeight,
      isFetching: isLoading,
      enableColumnGrouping,
    });
}

// Internal component that handles both modes
function DataGridContentReactQuery<TData extends object>({
  columns,
  initialSorting,
  containerHeight,
  estimatedRowHeight,
  debug,
  enableColumnGrouping,
  ...props
}: any) {
  const mode = 'react-query';
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [sorting, setSorting] = React.useState<SortingState>(initialSorting);
  const [columnSizing, setColumnSizing] = React.useState<ColumnSizingState>(
    () =>
      columns.reduce(
        (acc: any, column: { id: string; size: any }) => ({
          ...acc,
          [column.id as string]:
            typeof column.size === 'number' ? column.size : 150,
        }),
        {}
      )
  );

  // Update column sizing when columns change
  useEffect(() => {
    setColumnSizing((prev) =>
      columns.reduce(
        (acc: any, column: { id: string; size: any }) => ({
          ...acc,
          [column.id as string]:
            prev[column.id as string] ?? column.size ?? 150,
        }),
        {}
      )
    );
  }, [columns]);

    const { fetchData, fetchSize, filteredRequestBody } = props;

    const {
      data: queryData,
      fetchNextPage,
      isFetching,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [
        'virtualized-data',
        sorting,
        JSON.stringify(filteredRequestBody),
      ],
      queryFn: async ({ pageParam = 0 }) =>
        fetchData({
          pageParam: pageParam as number,
          fetchSize,
          sorting,
          filters: filteredRequestBody,
        }),
      initialPageParam: 0,
      getNextPageParam: (_lastGroup, groups) => groups.length,
      placeholderData: keepPreviousData,
    });

    const flatData = useMemo(() => {
      const infiniteData = queryData as
        | InfiniteData<ApiResponse<TData>>
        | undefined;
      return infiniteData?.pages.flatMap((page) => page.data) ?? [];
    }, [queryData]);

    const totalDBRowCount = queryData?.pages[0]?.meta?.totalRowCount ?? 0;

    // Setup the table with TanStack Table
    const table = useReactTable({
      data: flatData,
      columns,
      state: {
        sorting,
        columnSizing,
      },
      enableColumnResizing: true,
      columnResizeMode: 'onEnd',
      onColumnSizingChange: setColumnSizing,
      onSortingChange: setSorting,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      manualSorting: true,
      debugTable: debug,
      defaultColumn: {
        minSize: 50,
        maxSize: 1000,
        size: 150,
      },
    });

    const totalSize = useMemo(() => table.getTotalSize(), [table]);

    const rowVirtualizer = useVirtualizer({
      count: table.getRowModel().rows.length,
      estimateSize: () => estimatedRowHeight,
      getScrollElement: () => tableContainerRef.current,
      overscan: 5,
    });

    useEffect(() => {
      const element = tableContainerRef.current;
      if (!element) return;

      const checkScroll = () => {
        const { scrollHeight, scrollTop, clientHeight } = element;
        if (
          scrollHeight - scrollTop - clientHeight < 500 &&
          !isFetching &&
          flatData.length < totalDBRowCount
        ) {
          fetchNextPage();
        }
      };

      element.addEventListener('scroll', checkScroll);
      return () => element.removeEventListener('scroll', checkScroll);
    }, [fetchNextPage, isFetching, flatData.length, totalDBRowCount]);

    if (isLoading) {
      return (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height={containerHeight}
        >
          <CircularProgress />
        </Box>
      );
    }

    return renderGridContent({
      tableContainerRef,
      table,
      totalSize,
      rowVirtualizer,
      dataLength: flatData.length,
      totalRowCount: totalDBRowCount,
      estimatedRowHeight,
      containerHeight,
      isFetching,
      enableColumnGrouping,
    });
}

// Common UI rendering function used by both modes
function renderGridContent({
  tableContainerRef,
  table,
  totalSize,
  rowVirtualizer,
  dataLength,
  totalRowCount,
  estimatedRowHeight,
  containerHeight,
  isFetching,
  enableColumnGrouping,
}: any) {
  return (
    <Box>
      <Typography variant="body2" sx={{ mb: 1 }}>
        ({dataLength} di {totalRowCount} righe)
      </Typography>

      <Paper>
        <StyledTableContainer
          ref={tableContainerRef}
          sx={{
            height: containerHeight,
            overflow: 'auto',
          }}
        >
          <Table style={{ width: totalSize }}>
            <StyledTableHead>
              {table
                .getHeaderGroups()
                .map(
                  (headerGroup: {
                    id: React.Key | null | undefined;
                    headers: any[];
                  }) => (
                    <TableRow
                      key={headerGroup.id}
                      style={{
                        display: 'flex',
                        width: '100%',
                      }}
                    >
                      {headerGroup.headers.map((header) => {
                        const width = header.getSize();
                        return (
                          <TableCell
                            key={header.id}
                            colSpan={header.colSpan}
                            style={{
                              width: `${width}px`,
                              minWidth: `${width}px`,
                              maxWidth: `${width}px`,
                              boxSizing: 'border-box',
                              flex: `0 0 ${width}px`,
                              backgroundColor:
                                header.colSpan > 1 && enableColumnGrouping
                                  ? header.depth === 0
                                    ? TABLE_GROUP_HEADER_L1_COLOR
                                    : TABLE_GROUP_HEADER_L2_COLOR
                                  : TABLE_HEADER_COLOR,
                            }}
                          >
                            {!header.isPlaceholder && (
                              <NsTooltip
                                title={getTooltipContent(header)}
                                placement="top"
                              >
                                <Box
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 1,
                                    userSelect: 'none',
                                    width: 'fit-content',
                                  }}
                                >
                                  <Typography variant="h3">
                                    {flexRender(
                                      header.column.columnDef.header,
                                      header.getContext()
                                    )}
                                  </Typography>
                                  {header.column.getCanSort() &&
                                    !header.column.getIsGrouped() && (
                                      <Box
                                        component="span"
                                        onClick={header.column.getToggleSortingHandler()}
                                        sx={{
                                          cursor: 'pointer',
                                          display: 'flex',
                                          alignItems: 'center',
                                        }}
                                      >
                                        {{
                                          asc: (
                                            <ArrowUpwardIcon fontSize="small" />
                                          ),
                                          desc: (
                                            <ArrowDownwardIcon fontSize="small" />
                                          ),
                                        }[
                                          header.column.getIsSorted() as string
                                        ] ?? (
                                          <ImportExportIcon fontSize="small" />
                                        )}
                                      </Box>
                                    )}
                                </Box>
                              </NsTooltip>
                            )}
                            {header.column.getCanResize() && (
                              <Box
                                className={`resizer ${
                                  header.column.getIsResizing()
                                    ? 'isResizing'
                                    : ''
                                }`}
                                onMouseDown={header.getResizeHandler()}
                                onTouchStart={header.getResizeHandler()}
                                onClick={(e) => e.stopPropagation()}
                              />
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  )
                )}
            </StyledTableHead>

            <TableBody>
              <Box
                style={{
                  height: `${rowVirtualizer.getTotalSize()}px`,
                  width: '100%',
                  position: 'relative',
                }}
              >
                {rowVirtualizer
                  .getVirtualItems()
                  .map((virtualRow: { index: number; start: any }) => {
                    const row = table.getRowModel().rows[virtualRow.index];
                    const isEven = virtualRow.index % 2 === 0;

                    return (
                      <TableRow
                        key={row.id}
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          transform: `translateY(${virtualRow.start}px)`,
                          width: '100%',
                          display: 'flex',
                          height: `${estimatedRowHeight}px`,
                          borderBottom: (theme) => theme.custom.borders[0],
                          padding: 0,
                          backgroundColor: isEven ? '#f5f5f5' : 'white',
                          '&:hover': {
                            '& td': {
                              backgroundColor: (theme) =>
                                alpha(theme.palette.primary.main, 0.1),
                            },
                          },
                          '&:last-child': {
                            borderBottom: 'none',
                          },
                        }}
                      >
                        {row.getVisibleCells().map(
                          (cell: {
                            id: React.Key | null | undefined;
                            column: {
                              getSize: () => any;
                              columnDef: {
                                cell:
                                  | string
                                  | number
                                  | boolean
                                  | React.ComponentType<any>
                                  | React.ReactElement<
                                      any,
                                      string | React.JSXElementConstructor<any>
                                    >
                                  | Iterable<React.ReactNode>
                                  | React.ReactPortal
                                  | null
                                  | undefined;
                              };
                            };
                            getContext: () => any;
                          }) => (
                            <TableCell
                              key={cell.id}
                              sx={{
                                width: `${cell.column.getSize()}px`,
                                minWidth: `${cell.column.getSize()}px`,
                                maxWidth: `${cell.column.getSize()}px`,
                                boxSizing: 'border-box',
                                flex: `0 0 ${cell.column.getSize()}px`,
                                borderRight: (theme) => theme.custom.borders[0],
                                height: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                                fontSize: '0.875rem',
                                backgroundColor: 'transparent',
                                '&:last-child': {
                                  borderRight: 'none',
                                },
                              }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          )
                        )}
                      </TableRow>
                    );
                  })}
              </Box>
            </TableBody>
          </Table>
        </StyledTableContainer>
      </Paper>

      {isFetching && (
        <Box display="flex" justifyContent="center" alignItems="center" mt={1}>
          <CircularProgress size={24} />
          <Typography sx={{ ml: 1 }}>Caricamento in corso...</Typography>
        </Box>
      )}
    </Box>
  );
}
