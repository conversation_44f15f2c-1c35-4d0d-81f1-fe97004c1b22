plugins {
    id "ns-patch" version "${patchPluginVersion}"
}

patch {
    includeVersionFile true
    basename = 'WEB-CSP'
}



releaseNotes{
    withAs true
    
    // withDb a false per evitare che aggiunga la sezione per script db presenti nello stesso progetto.
	withDb false

    //modalitaApplicazioneDB "senza_fermo"
    modalitaApplicazioneDB "con_fermo"

    applicazionePatchAS {
        descrizione = "Il presente documento descrive la procedura di aggiornamento (patch) del prodotto web-csp. La patch è rilasciata sotto forma di file compresso (.zip). Nel documento sono inoltre elencati impatti, modifiche ed interventi oggetto del presente aggiornamento."
        elenco = ["Effettuare il login alla macchina AS come utente root",
                  "Copiare il file zip in una cartella temporanea e scompattarlo",
                  "Stoppare il servizio httpd",
                  "Eseguire lo script 'install.sh' presente nella cartella application_server",
                  "Riavviare il servizio httpd"
        ]
    }
    /*
    interventiEseguiti {
         descrizione = "Rilascio per il ticket 43103 di SERVIZI_CSP_1.01.04"            
    } 


    
    */

    
    modificheEvolutive{
        descrizione = "Contratto CIG B2BD866C4A - PLO21 - Interventi migliorativi per l’operatività della Settima Sezione Penale"
    }


    dipendenzeSistemi{
        descrizione = "Installare prima la patch SERVIZI-CSP-1.04.00"
    }

    modificheDB {
        descrizione = "Sono riportate nella patch CASSAZIONE_PENALE_DB_1.04.00"
    }



/*     modificheConfigurazione {
        descrizione = "Nel file .env.production presente nella cartella CONF della patch sono riportate le configurazioni attualmente disponibili in ambiente di esercizio."
    }

    documentazione {

        descrizione = "Rilasciato il manuale utente MG-CSP-MU-CASS-PENALE-001-NS.pdf"
    } */


}


task copyBuild(type: Copy) {
    from "$rootDir/csp-client/build"
    into "$buildDir/application_server/csp-client"
}

copyBuild.dependsOn ':csp-client:build'

afterEvaluate{
    tasks['patch'].dependsOn copyBuild
}

