import React, { useState } from 'react';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import { DetailPartiProps } from '../interfaces';

export default function DetailParti({ parti }: DetailPartiProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const theme: any = useTheme();
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  const renderPartyDisplay = (displayParte: string) => {
      const  primoMeno = displayParte.indexOf('-');
      const secondoMeno = displayParte.indexOf('-', primoMeno+1);
      if (secondoMeno) {
          const  parteBold = displayParte.substring(0, secondoMeno);
          console.log('parte1:',parteBold)
          let parteDue = displayParte.substring(secondoMeno);
          console.log('parte2:', parteDue)
          return <><span style={{fontWeight: 600}}>{parteBold}</span>{parteDue}</>;
      }
      return displayParte;
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <Grid container spacing={4}>
      <Grid item xs={12} mt={3}>
        <Box sx={scrollBar}>
          <Grid item p={3} border={1} xs={12}>
            {parti?.map((parte: any, i: number) => {
              return (
                <Box key={i} mb={i === parti.length - 1 ? 0 : 3}>
                  <Typography variant="h6" sx={{fontWeight: 400 }} ml={2}>
                    {renderPartyDisplay(parte.displayParte)}
                  </Typography>
                  {parte.penaleDifensoreParti &&
                    parte.penaleDifensoreParti.map(
                      (penaleDifensoreParti: any) => {
                        return (
                            <Box key={penaleDifensoreParti.id}>
                          <Typography
                            variant="h6"
                            sx={{fontWeight: 400 }}
                            key={penaleDifensoreParti.id}
                          >
                              <span style={{marginLeft: '2.5rem'}}>
                            - {penaleDifensoreParti.cognome}{' '}
                            {penaleDifensoreParti.nome} -{' '}
                            <em>{penaleDifensoreParti.penaleParam.descrizione}{' '}</em>
                            {penaleDifensoreParti?.deceduto == '1'
                              ? '- DEC.'
                              : ''}
                              </span>
                          </Typography>
                            </Box>
                        );
                      }
                    )}
                  {parte.penaleControParti && (
                    <Box>
                      <Typography variant="h6" ml={2}>
                       <span style={{marginLeft: '2.5rem', fontWeight: 600}}> C/</span>
                      </Typography>
                      {parte.penaleControParti.map((penaleControParti: any) => {
                        return (
                            <Box key={penaleControParti.id}>
                          <Typography
                            variant="h6"
                            ml={2}
                            sx={{fontWeight: 400 }}
                            key={penaleControParti.id}
                          >
                              <span style={{marginLeft: '3.5rem'}}>{renderPartyDisplay(penaleControParti.displayParte)}</span>
                          </Typography>
                            </Box>
                        );
                      })}
                    </Box>
                  )}
                </Box>
              );
            })}
          </Grid>
        </Box>
      </Grid>
    </Grid>
  );
}
