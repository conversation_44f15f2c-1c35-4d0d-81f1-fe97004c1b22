import { Box, MenuItem, Select, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { SceltaOscuramentoModalProps } from '../interfaces';
import { useEffect, useState } from 'react';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
export default function SceltaOscuramentoModal({
  firma,
  origine,
  elaborazioneDeposito,
   oscuramentoDeskCsp
}: Readonly<SceltaOscuramentoModalProps>) {
  const [tipoOscuramento, setTipoOscuramento] = useState<string>('');
  const { t } = useTranslation();

  useEffect(() => {
    if (origine !== "LOCALE" || oscuramentoDeskCsp) {
        elaborazioneDeposito.oscuramentoDati = "Si"
        setTipoOscuramento("Si");
    }
  }, []);

    const handleChangeValue = (e: any) => {
        elaborazioneDeposito.oscuramentoDati = e.target.value;
        setTipoOscuramento(e.target.value);
    }

    return (
      <Box>
        <Typography variant="h5">
          {t("depositi.sceltaOscuramentoModal.inCasoDiDiffusioneProvv")}
        </Typography>
          {origine === 'LOCALE' &&
        <Box display="flex"
          sx={{
            marginTop: '1rem',
            marginBottom: '1rem',
          }}
        >
          <WarningAmberIcon
            sx={{
              color: 'red',
              marginRight: '5px',
              fontSize: '30px',
              marginLeft: '0',
            }}
          />
          <Typography variant="h5" color="red">
            {t('depositi._fileBusta.fileBustaNotifica')}
          </Typography>
        </Box>
          }
        <Select
          size="small"
          label="Tipo oscuramento"
          name="tipoOscuramento"
          value={tipoOscuramento}
          onChange={(e) => handleChangeValue(e)}
          sx={{ minWidth: 250 }}
          readOnly={origine !== "LOCALE"}
        >
          <MenuItem value="Si">
            {t('depositi.sceltaOscuramentoModal.si')}
          </MenuItem>
            {origine === 'LOCALE' &&
          <MenuItem value="Nessuno">
            {t('depositi.sceltaOscuramentoModal.no')}
          </MenuItem>
            }
        </Select>
        <Box mt={5} display="flex" justifyContent="space-between">
          <NsButton
            disabled={tipoOscuramento === ''}
            variant="contained"
            onClick={firma}
          >
            {t('buttonsLabel.conferma')}
          </NsButton>
        </Box>
      </Box>
  );
}
