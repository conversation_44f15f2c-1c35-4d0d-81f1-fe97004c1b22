import { Box, Grid, MenuItem, TextField, Typography } from '@mui/material';
import {
  NsButton,
  NsSelectAutocomplete,
  NsTextInput,
  useNotifier
} from '@netservice/astrea-react-ds';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getSiglaSezione } from 'src/utils/Authentication';
import {
  DepositoFilterProps,
  MenuItemProps,
  ObjectKeyValues,
} from '../interfaces';
import { getCollegioOptions, getSezioniOptions } from '../shared/Utils';
import { Label } from '@mui/icons-material';
import { getFallbackSorting } from './constants/depositiSorting';

export default function DepositoFilter({
  magistrati,
  filteredRequestBody,
  setDepositanteNC,
  setFilteredRequestBody,
  closeModal,
  resetCheckboxFilters,
}: DepositoFilterProps) {
  const { t } = useTranslation();
  const { notify } = useNotifier();

  const [tipoUdienza, setTipoUdienza] = useState<string>('');
  const [annoRicorso, setAnnoRicorso] = useState<string>('');
  const [numeroRicorso, setNumeroRicorso] = useState<string>('');
  const [tipiTab, setTipiTab] = useState<string>('');
  const [collegio, setCollegio] = useState<string>('');
  const [sezione, setSezione] = useState<string>('');
  const [oscuramentoDeskCsp, setOscuramentoDeskCsp] = useState<string>('');
  const [dataDeposito, setDataDeposito] = useState<string>('');
  const [fullName, setFullName] = useState<string>('');
  const [dataUdienza, setDataUdienza] = useState<string>('');
  const [depositante, setDepositante] = useState<string>('');
  const [stato, setStato] = useState<string>('');
  const [anomalia, setAnomalia] = useState<string>('');

  const collegioOptions = getCollegioOptions();

  const sezioniOptions = getSiglaSezione();

  useEffect(() => {
    if (sezioniOptions != 'CE') {
      setSezione(sezioniOptions);
    }
  }, [sezioniOptions]);

  const sezioniOptionsCE = getSezioniOptions();

  const anomalieOptions: MenuItemProps[] = [
    { value: '', label: t('common.selezionaOpzione') },
    { value: '1', label: 'Warning' },
    { value: '2', label: 'Error' },
    { value: '3', label: 'Fatal' },
  ];

  const anomalieLabel: ObjectKeyValues = {
    1: 'Warning',
    2: 'Error',
    3: 'Fatal',
  };

  const oscuratoLabel: ObjectKeyValues = {
    1: 'Si',
    0: 'No',
  };

  const attoOptions: MenuItemProps[] = [
    { value: '', label: t('common.selezionaOpzione') },
    { value: 'MinutaOrdinanza', label: 'Minuta di Ordinanza' },
    { value: 'MinutaSentenza', label: 'Minuta di Sentenza' },
    { value: 'Ordinanza', label: 'Ordinanza' },
    { value: 'Sentenza', label: 'Sentenza' },
  ];

  const attoOptionsLabels: ObjectKeyValues = {
    MinutaOrdinanza: 'Minuta di Ordinanza',
    MinutaSentenza: 'Minuta di Sentenza',
    Ordinanza: 'Ordinanza',
    Sentenza: 'Sentenza',
  };

  const statoOptions: MenuItemProps[] = [
    { value: '', label: t('common.selezionaOpzione') },
    { value: 'NEW', label: t('depositi.depositoFilter.daAccettare') },
    { value: 'ACCETTATO', label: t('depositi.depositoFilter.minutaAccettata') },
    { value: 'RIFIUTATO', label: t('depositi.depositoFilter.rifiutato') },
    { value: 'PUBBLICATO', label: t('depositi.depositoFilter.pubblicato') },
    {
      value: 'ERROREPUBBLICAZIONE',
      label: t('depositi.depositoFilter.inErroreDiPubblicazione'),
    },
  ];

  const statoOptionsLabels: ObjectKeyValues = {
    NEW: 'Da accettare',
    ACCETTATO: 'Minuta Accettata',
    RIFIUTATO: 'Rifiutato',
    PUBBLICATO: 'Pubblicato',
    ERROREPUBBLICAZIONE: 'In errore pubblicazione',
  };

  useEffect(() => {
    if (!filteredRequestBody) return;

    const params = filteredRequestBody.criteriaParameters || {};

    setTipoUdienza(params.tipoUdienza || '');
    setAnnoRicorso(params.annoRicorso || '');
    setNumeroRicorso(params.numeroRicorso || '');

    if (params.tipiProvvedimento) {
      if (typeof params.tipiProvvedimento === 'string') {
        setTipiTab(params.tipiProvvedimento);
      } else if (Array.isArray(params.tipiProvvedimento) && params.tipiProvvedimento.length === 1) {
        setTipiTab(params.tipiProvvedimento[0]);
      }
    } else if (params.tipoProvvedimento) {
      setTipiTab(params.tipoProvvedimento);
    } else {
      setTipiTab('');
    }

    setCollegio(params.aula || '');
    setSezione(params.sezioneUdienza || '');
    setOscuramentoDeskCsp(params.oscuramentoDeskCsp || '');

    if (params.dataDepositoA) {
      const date = new Date(params.dataDepositoA);
      setDataDeposito(date.toISOString().split('T')[0]);
    } else {
      setDataDeposito('');
    }

    if (params.dataUdienzaA) {
      const date = new Date(params.dataUdienzaA);
      setDataUdienza(date.toISOString().split('T')[0]);
    } else {
      setDataUdienza('');
    }

    setDepositante(params.cfMittente || '');
    
    // Gestione del depositante - troviamo il nome completo dal codice fiscale
    if (params.cfMittente) {
      const foundMagistrato = magistrati.find(
        (magistrato: any) => magistrato?.codiceFiscale === params.cfMittente
      );
      if (foundMagistrato) {
        setFullName(`${foundMagistrato.nome} ${foundMagistrato.cognome}`);
      } else {
        setFullName('');
      }
    } else {
      setFullName('');
    }
    
    setStato(params.stato || '');
    setAnomalia(params.anomalia || '');

    const currentSezione = getSiglaSezione();
    if (currentSezione !== 'CE' && !params.sezioneUdienza) {
      setSezione(currentSezione);
    }
  }, [filteredRequestBody, magistrati]);

  const handleAnnulla = () => {
    closeModal();
  };

  const sezioniMemo = useMemo(
    () => (
      <MenuItem key={sezioniOptions} value={sezioniOptions}>
        {sezioniOptions}
      </MenuItem>
    ),
    [sezioniOptions]
  );

  const sezioniMemoCE = useMemo(
    () =>
      sezioniOptionsCE.map((sezione, i) => (
        <MenuItem key={i} value={sezione.value}>
          {sezione.label}
        </MenuItem>
      )),
    [sezioniOptionsCE]
  );

  const collegioMemo = useMemo(
    () =>
      collegioOptions.map((collegio, i) => (
        <MenuItem key={i} value={collegio.value}>
          {collegio.label}
        </MenuItem>
      )),
    [collegioOptions]
  );

  const anomalieMemo = useMemo(
    () =>
      anomalieOptions.map((anomalia, i) => (
        <MenuItem key={i} value={anomalia.value}>
          {anomalia.label}
        </MenuItem>
      )),
    [anomalieOptions]
  );

  const statoMemo = useMemo(
    () =>
      statoOptions.map((stato, i) => (
        <MenuItem key={i} value={stato.value}>
          {stato.label}
        </MenuItem>
      )),
    [statoOptions]
  );

  const attoMemo = useMemo(
    () =>
      attoOptions.map((atto, i) => (
        <MenuItem key={i} value={atto.value}>
          {atto.label}
        </MenuItem>
      )),
    [attoOptions]
  );

  const handleApplicaFiltri = () => {
    const numeroRicorsoRes = numeroRicorso || null;
    const annoRicorsoRes = annoRicorso || null;
    const tipiTabRes = tipiTab || null;
    const tipoUdienzaRes = tipoUdienza || null;
    const collegioRes = collegio || null;
    const sezioneRes = sezione || null;
    const oscuramentoDeskCspRes = oscuramentoDeskCsp || null;
    const dataDepositoRes = dataDeposito || null;
    const dataUdienzaRes = dataUdienza || null;
    const depositanteRes = depositante || null;
    const statoRes = stato || null;
    const anomaliaRes = anomalia || null;

    let dataDepositoRes1;
    let dataDepositoRes2;
    if (dataDepositoRes != null) {
      dataDepositoRes1 = new Date(dataDepositoRes).setHours(23, 59, 59, 0);
      dataDepositoRes2 = new Date(dataDepositoRes).setHours(0, 0, 59, 0);
    }

    let dataUdienzaRes1;
    let dataUdienzaRes2;
    let dataUdienzaError = false;

    if (dataUdienzaRes != null) {
      dataUdienzaRes1 = new Date(dataUdienzaRes).setHours(23, 59, 59, 0);
      dataUdienzaRes2 = new Date(dataUdienzaRes).setHours(0, 0, 59, 0);
      const today = new Date();

      if (
        new Date(dataUdienzaRes).setHours(23, 59, 0, 0) >
        today.setHours(23, 59, 0, 0)
      ) {
        dataUdienzaError = true;
      }
    }

    if (dataUdienzaError) {
      notify({
        type: 'error',
        message: 'Nessuna udienza per la data inserita',
      });
      return null;
    }

    const modalFilters: any = {};

    // Add filter values only if they are set
    if (tipoUdienzaRes) modalFilters.tipoUdienza = tipoUdienzaRes;
    if (numeroRicorsoRes) modalFilters.numeroRicorso = numeroRicorsoRes;
    if (annoRicorsoRes) modalFilters.annoRicorso = annoRicorsoRes;
    if (collegioRes) modalFilters.aula = collegioRes;
    if (sezioneRes) modalFilters.sezioneUdienza = sezioneRes;
    if (oscuramentoDeskCspRes) modalFilters.oscuramentoDeskCsp = oscuramentoDeskCspRes;
    if (dataDepositoRes) {
      modalFilters.dataDepositoDa = dataDepositoRes2;
      modalFilters.dataDepositoA = dataDepositoRes1;
    }
    if (dataUdienzaRes) {
      modalFilters.dataUdienzaDa = dataUdienzaRes2;
      modalFilters.dataUdienzaA = dataUdienzaRes1;
    }
    if (depositanteRes) modalFilters.cfMittente = depositanteRes;
    if (statoRes) modalFilters.stato = statoRes;
    if (anomaliaRes) modalFilters.anomalia = anomaliaRes;

    if (tipiTabRes) {
      // For single values, use tipoProvvedimento (singular) as that's what the backend checks
      if (!Array.isArray(tipiTabRes) || tipiTabRes.length === 1) {
        const singleValue = Array.isArray(tipiTabRes) ? tipiTabRes[0] : tipiTabRes;
        modalFilters.tipoProvvedimento = singleValue;
      } else {
        // For multiple values (like when selecting both Sentenza and Ordinanza),
        // use tipiProvvedimento (plural)
        modalFilters.tipiProvvedimento = tipiTabRes;
      }
    }

    // Reset checkbox filters when modal filters are applied
    if (resetCheckboxFilters) {
      resetCheckboxFilters();
    }

    // Preserve existing pagination and order settings
    const requestBody = {
      pageInfo: {
        pageNumber: 0,
        pageSize: filteredRequestBody?.pageInfo?.pageSize || 25,
        order: filteredRequestBody?.pageInfo?.order || getFallbackSorting(),
      },
      criteriaParameters: modalFilters,
    };

    setDepositanteNC(fullName);
    setFilteredRequestBody(requestBody);
    closeModal();
  };

  const getSelectedUtente = (value: string) => {
    const fullName = magistrati.find(
      (magistrato: any) => magistrato?.codiceFiscale == value
    );
    setDepositante(value);
    if (!fullName) {
      setFullName(``);
      return;
    }

    setFullName(`${fullName.nome} ${fullName.cognome}`);
  };

  const uniqueMagistrati = magistrati?.filter((magistrato: { codiceFiscale: any; }, index: any, self: any[]) =>
    index === self.findIndex((m) => m.codiceFiscale === magistrato.codiceFiscale)
  );

  return (
    <Grid container>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>{t('depositi.depositoFilter.sezione')}</Typography>
        {sezioniOptions === 'CE' ? (
          <NsSelectAutocomplete
          name="select"
          defaultValue={sezione}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setSezione(e.value)}
        >
          {sezioniMemoCE}
        </NsSelectAutocomplete>
        ) : <NsTextInput
          sx={{
            width: '250px'
          }}
          defaultValue={sezioniOptions}
          disabled
        />}
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.dataUdienza')}
        </Typography>
        <TextField
          size="small"
          type="date"
          value={dataUdienza}
          label=""
          name="dataUdienza"
          sx={{ minWidth: 250 }}
          onChange={(e) => setDataUdienza(e.target.value)}
        />
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.tipoUdienza')}
        </Typography>
        <NsSelectAutocomplete
          name="tipoUdienza"
          defaultValue={tipoUdienza}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setTipoUdienza(e.value)}
        >
          <MenuItem value="">{t('common.selezionaOpzione')}</MenuItem>
          <MenuItem value="CC">CC</MenuItem>
          <MenuItem value="PU">PU</MenuItem>
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>{t('depositi.depositoFilter.collegio')}</Typography>
        <NsSelectAutocomplete
          name="collegio"
          defaultValue={collegio}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setCollegio(e.value)}
        >
          {collegioMemo}
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.numeroRicorso')}
        </Typography>
        <TextField
          name="numeroRicorso"
          label=""
          type="number"
          size="small"
          value={numeroRicorso}
          sx={{ minWidth: 250 }}
          onChange={(e) => setNumeroRicorso(e.target.value)}
        />
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.annoRicorso')}
        </Typography>
        <TextField
          size="small"
          name="annoRicorso"
          label=""
          value={annoRicorso}
          type="number"
          sx={{ minWidth: 250 }}
          onChange={(e) => setAnnoRicorso(e.target.value)}
        />
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.depositante')}
        </Typography>
        <NsSelectAutocomplete
          name="depositante"
          defaultValue={fullName}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => getSelectedUtente(e.value)}
        >
          <MenuItem value="">{t('common.selezionaOpzione')}</MenuItem>
          {uniqueMagistrati?.length > 0 &&
            uniqueMagistrati?.map((magistrato: any, i: number) => {
              return (
                <MenuItem key={i} value={magistrato.codiceFiscale}>
                  {`${magistrato.nome} ${magistrato.cognome} `}
                </MenuItem>
              );
            })}
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>{t('depositi.depositoFilter.stato')}</Typography>
        <NsSelectAutocomplete
          name="stato"
          defaultValue={statoOptionsLabels[stato]}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setStato(e.value)}
        >
          {statoMemo}
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>{t('depositi.depositoFilter.atto')}</Typography>
        <NsSelectAutocomplete
          name="atto"
          defaultValue={attoOptionsLabels[tipiTab]}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setTipiTab(e.value)}
        >
          {attoMemo}
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.dataDeposito')}
        </Typography>
        <TextField
          size="small"
          type="date"
          label=""
          name="dataDeposito"
          value={dataDeposito}
          sx={{ minWidth: 250 }}
          onChange={(e) => setDataDeposito(e.target.value)}
        />
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>
          {t('depositi.depositoFilter.oscuramentoDeskCsp')}
        </Typography>
        <NsSelectAutocomplete
          name="oscuramentoDeskCsp"
          defaultValue={oscuratoLabel[oscuramentoDeskCsp]}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setOscuramentoDeskCsp(e.value)}
        >
          <MenuItem value="">{t('common.selezionaOpzione')}</MenuItem>
          <MenuItem value="1">{t('common.yes')}</MenuItem>
          <MenuItem value="0">{t('common.no')}</MenuItem>
        </NsSelectAutocomplete>
      </Grid>
      <Grid item xs={4} mt={2}>
        <Typography mb={1}>{t('depositi.depositoFilter.diagnosi')}</Typography>
        <NsSelectAutocomplete
          name="diagnosi"
          defaultValue={anomalieLabel[anomalia]}
          size="small"
          sx={{ width: '250px' }}
          changed={(e: any) => setAnomalia(e.value)}
        >
          {anomalieMemo}
        </NsSelectAutocomplete>
      </Grid>
      <Grid item container justifyContent="space-between" xs={12} mt={5}>
        <NsButton onClick={handleAnnulla}>{t('buttonsLabel.annulla')}</NsButton>
        <NsButton variant="contained" onClick={handleApplicaFiltri}>
          {t('common.applicaFiltri')}
        </NsButton>
      </Grid>
    </Grid>
  );
}
