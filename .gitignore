# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# Using yarn, avoid package-lock.json
package-lock.json

# testing
/coverage

# next.js
.next/
/out/

# production
/build


# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*



# vercel
.vercel

# IntelliJ
*.iml

# VS Code
.vscode
.idea/

/portale-cassazione-penale/node_modules
/nbproject/private/
nbproject/

patch/build/
distribution/patch/src/main/scripts/portale-cassazione-penale/
/.gradle/
/.project
.gradle
.settings
.classpath
.project
*.iml
/.nb-gradle/
.nb-gradle-properties
/distribution/patch/build/
/portale-cassazione-penale/build/
.yalc
yalc.lock
portale-cassazione-penale/.yalc/
portale-cassazione-penale/yalc.lock

distribution/build/
/gradlew
/gradlew.bat
/gradle/wrapper/gradle-wrapper.jar
/gradle/wrapper/gradle-wrapper.properties
.history/
# local env files non vanno pushati!
csp-client/.env.local
csp-client/.env.develop*
csp-client/.env.test*
csp-client/.env.production*
csp-client/.env
csp-client/__generated__/