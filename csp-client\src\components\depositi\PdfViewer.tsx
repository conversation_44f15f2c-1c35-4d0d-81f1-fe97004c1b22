import { useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
// import pdf worker as a url, see `next.config.js` and `pdf-worker.js`
import workerSrc from './pdf-worker';

pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

export default function PdfViewer({ blob }: any) {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfString, setPdfString] = useState('');

  useEffect(() => {
    // Create URL from the Blob
    if (blob) {
      setPdfString(URL.createObjectURL(blob));
    }
  }, [blob]);

  function onDocumentLoadSuccess({ numPages }: any) {
    setNumPages(numPages);
  }
  return (
    <div>
      {/* <embed
        src={URL.createObjectURL(blob)}
        type="application/pdf"
        width="100%"
        height="100%"
      /> */}
      <Document
        file={blob}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={(e) => {
          console.log(e);
        }}
      >
        <Page pageNumber={pageNumber} />
      </Document>
    </div>
  );
}
