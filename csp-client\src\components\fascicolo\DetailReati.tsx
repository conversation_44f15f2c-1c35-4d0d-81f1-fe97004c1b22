import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { DetailReatiProps } from '../interfaces';

export default function DetailReati({ reati }: DetailReatiProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const iconsStyle = { color: '#2e5a60' };
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };

  return (
    <Grid container>
      <Grid item xs={12} mt={3}>
        <Box sx={scrollBar}>
          <Grid item p={3} border={1} xs={12}>
            {reati?.map((reato: any, i: number) => {
              return (
                <Typography
                  key={i}
                  mb={i === reati.length - 1 ? 0 : 3}
                  variant="h4"
                >
                  {t('fascicoli.detailReati.reato')} {reato.displayReato}
                </Typography>
              );
            })}
          </Grid>
        </Box>
      </Grid>
    </Grid>
  );
}
