import { Box, Grid, Typography, useTheme } from '@mui/material';
import { DetailRiunitiProps, RicorsoUdienzaRiunito } from '../interfaces';
import { useEffect, useState } from 'react';
import APIWrapper from '../../utils/APIWrapper';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';

export default function DetailRiuniti({
  idRicUdienza,
}: Readonly<DetailRiunitiProps>) {
  const theme: any = useTheme();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const scrollBar = {
    overflow: 'auto',
    maxHeight: 400,
  };
  const [riuniti, setRiuniti] = useState<Array<RicorsoUdienzaRiunito> | null>(
    null
  );

  useEffect(() => {
    const fetchRiuniti = async () => {
      try {
        const api = await APIWrapper;
        const riunitiApi = await api.call('GET', `ricorsi/riuniti/${idRicUdienza}`);
        setRiuniti(riunitiApi);
      } catch (error) {
        console.error('Errore nel caricamento dei riuniti:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRiuniti();
  }, [idRicUdienza]);

  return (
    <>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
      <Grid container>
        <Grid item xs={12}>
          <Box sx={scrollBar}>
            <Grid item p={2} xs={12} border={1}>
              {riuniti?.map((riunito: any, i: number) => {
                return (
                  <Typography key={'riu-' + i} variant="h4" p={2}>
                    + {riunito.numero}/{riunito.anno} (R)
                  </Typography>
                );
              })}
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
}