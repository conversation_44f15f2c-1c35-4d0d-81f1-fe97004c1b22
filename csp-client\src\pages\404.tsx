import { Box, Button, Grid, Typography, useTheme } from '@mui/material';
import { useRouter } from 'next/router';
import {useTranslation} from "next-i18next";

/*
* This component handles http 404 error
*   The server cannot find the requested resource.
*   In the browser, this means the URL is not recognized.
*   In an API, this can also mean that the endpoint is valid but the resource itself does not exist.
*   Servers may also send this response instead of 403 Forbidden to hide the existence of a resource
*   from an unauthorized client. This response code is probably the most well known due to its frequent
*   occurrence on the web.
* */
const Custom404 = () => {
    const {t} = useTranslation();
    const theme: any = useTheme();
    const router = useRouter();

    const handleGoBack = () => {
        router.back();
    };

    return (
        <Grid p={2}>
            <Box
                border={theme.custom.borders[1]}
                p={3}
                width={'30%'}
                borderRadius="5px"
            >
                <Typography
                    color={'primary'}
                    sx={{ fontSize: '4rem !important', fontWeight: 'bold' }}
                    mb={2}
                >
                    404
                </Typography>
                <Typography variant="h2" mb={2}>
                    {t('pages.404.paginaORisorsaNonTrovata')}
                </Typography>
                <Typography mb={2} variant="body1">
                    {t('pages.404.indirizzoWebCorretto')}
                </Typography>
                <Typography mb={2} variant="body1">
                    {t('pages.404.seIndirizzoIncollatoControllalo')}
                </Typography>
                <Typography mb={2} variant="body1">
                    {t('pages.404.erroreAncoraPresenteContattaHelpDesk')}
                </Typography>
                <Button variant="contained" color="primary" onClick={handleGoBack}>
                    {t('pages.404.tornaAllaPaginaPrecedente')}
                </Button>
            </Box>
        </Grid>
    );
};

export default Custom404;
