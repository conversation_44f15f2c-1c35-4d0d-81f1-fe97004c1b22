import { Box, Typography, useTheme } from '@mui/material';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import RelayTable from '../shared/RelayTable';
import { GetDiagnosyColumns, formatDate, stateMap } from '../shared/Utils';

interface DiagnosiProps {
  rows: any;
  idNrg?: string;
  idCat?: any;
}

export default function Diagnosi({ rows, idNrg, idCat }: DiagnosiProps) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [selected, setSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stories, setStories] = useState<[] | any>([]);

  const buttonsStyle = {
    padding: 1,
    border: theme.custom.borders[0],
    cursor: 'pointer',
    width: '200px',
    color: '#2E5A60',
  };

  useEffect(() => {
    if (!selected && idCat && idCat !== -1) {
      setIsLoading(true);
      getStories();
    }
  }, [selected, idCat]);

  const getStories = async () => {
    try {
      if (!idCat || idCat === -1) {
        setIsLoading(false);
        return;
      }

      const api = await APIWrapper;
      const data = await api.call(
        'GET',
        `monitoraggio/trackingProvvedimentoByIdCat/${idCat}`
      );

      const map = data.map((storie: any) => {
        let descrizione = '';
        if (
          storie.stato === 'BUSTA_RIFIUTATA' &&
          storie.prevStato === 'INVIATO_IN_CANCELLERIA_RELATORE'
        ) {
          descrizione = 'Busta rifiutata';
        } else if (storie.stato === 'BUSTA_RIFIUTATA') {
          descrizione = 'Busta rifiutata al Presidente';
        } else {
          descrizione = stateMap[storie.stato];
        }

        return {
          data: formatDate(storie.oggi, 'DD/MM/YYYY [-] HH:mm'),
          codice: storie.stato,
          descrizione: descrizione,
          note: storie.note,
        };
      });
      setStories([...map]);
    } catch {
      console.log('error', 'Errore durante la ricerca');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Box display="flex">
        <Typography
          variant="body1"
          sx={buttonsStyle}
          bgcolor={selected ? '#cce2df' : ''}
          onClick={() => setSelected(true)}
        >
          {t('depositi._diagnosi.diagnosi')}
        </Typography>
        <Typography
          variant="body1"
          sx={buttonsStyle}
          bgcolor={!selected ? '#cce2df' : ''}
          onClick={() => setSelected(false)}
        >
          {t('depositi._diagnosi.storiaProvvedimento')}
        </Typography>
      </Box>
      <RelayTable
        maxHeight="354px"
        rows={!selected ? stories : rows}
        columns={GetDiagnosyColumns(!selected ? 'stories' : '')}
      />
      <NsFullPageSpinner isOpen={isLoading} value={1} />
    </>
  );
}
