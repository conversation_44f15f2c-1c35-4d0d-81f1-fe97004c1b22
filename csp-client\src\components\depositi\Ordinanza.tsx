import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const style = {
  background: '#E8E8E8',
  cursor: 'pointer',
  p: 1,
};

export default function Ordinanza({ deposito }: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [clicked, setClicked] = useState<boolean>(true);

  const selectedStyle = {
    background: 'white',
    cursor: 'pointer',
    p: 1,
    border: theme.custom.borders[0],
  };

  const handleClick = (button: string) => {
    if ((button === 'nonOsc' && clicked) || (button === 'osc' && !clicked)) {
      return;
    }
    setClicked(!clicked);
  };
  return (
    <>
      {deposito?.tipo == 'MinutaSentenza' && (
        <Typography variant="h1">
          {t('depositi.ordinanza.minutaDiSentenza')}
        </Typography>
      )}
      {deposito?.tipo == 'MinutaOrdinanza' && (
        <Typography variant="h1">
          {t('depositi.ordinanza.minutaDiOrdinanza')}
        </Typography>
      )}
      {deposito?.tipo == 'Sentenza' && (
        <Typography variant="h1">{t('depositi.ordinanza.sentenza')}</Typography>
      )}
      {deposito?.tipo == 'Ordinanza' && (
        <Typography variant="h1">
          {t('depositi.ordinanza.ordinanza')}
        </Typography>
      )}
      <Grid mt={2} container>
        <Grid item xs={6} md={6} onClick={() => handleClick('nonOsc')}>
          <Typography sx={clicked ? selectedStyle : style}>
            {t('depositi.ordinanza.nonOscurata')}
          </Typography>
        </Grid>
        <Grid item xs={6} md={6} onClick={() => handleClick('osc')}>
          <Typography sx={!clicked ? selectedStyle : style}>
            {t('depositi.ordinanza.oscurata')}
          </Typography>
        </Grid>

        <Grid item mt={2} xs={12}>
          {clicked ? (
            <Box>{t('depositi.ordinanza.oscuramentoDeskCsp')}</Box>
          ) : (
            <Box>{t('depositi.ordinanza.nonOscurato')}</Box>
          )}
        </Grid>
      </Grid>
    </>
  );
}
