import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Box, Container, Grid, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import packageJson from '../../../package.json';
import APIWrapper from 'src/utils/APIWrapper';
import { NsButton, useNotifier } from '@netservice/astrea-react-ds';
import { errorLogger, Severity } from '../../utils/errorLogger';

const InfoPage: React.FC = () => {
  const { t } = useTranslation();
  const [backendVersion, setBackendVersion] = useState<string>('');

  const { notify } = useNotifier();

  useEffect(() => {
    const getVersion = async () => {
      try {
        const api = await APIWrapper;
        const version = await api.getBackendVersion();
        setBackendVersion(version);
      } catch (error) {
        errorLogger.log({
          message: 'Error getting backend version',
          severity: Severity.ERROR,
          context: { error },
          component: 'InfoPage'
        });
      }
    };

    getVersion();
  }, []);

  const cspFrontendVersion = packageJson.version;

  const downloadErrors = () => {
    try {
      const errors = errorLogger.getLogs();
      if (errors.length === 0) {
        notify({
          type: 'warning',
          message: t('info.noErrors'),
        });
        return;
      }

      const blob = new Blob([JSON.stringify(errors, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = 'errors.json';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notify({
        type: 'success',
        message: t('info.errorsDownloaded'),
      });
    } catch (error) {
      errorLogger.log({
        message: 'Error downloading errors',
        severity: Severity.ERROR,
        context: { error },
        component: 'InfoPage'
      });
      notify({
        type: 'error',
        message: t('info.errorDownloadingErrors'),
      });
    }
  };

  return (
    <Container maxWidth="md">
      <Box my={4} border={1} borderColor="grey.300" p={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('info.versioni')}
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              CSP Client (Frontend): <b>{cspFrontendVersion}</b>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1">
              Servizi CSP (Backend): <b>{backendVersion}</b>
            </Typography>
          </Grid>
        </Grid>
        <Box mt={2}>
          <NsButton
            variant="contained"
            color="primary"
            onClick={downloadErrors}
          >
            {t('info.downloadErrors')}
          </NsButton>
        </Box>
      </Box>
    </Container>
  );
};

export default InfoPage;
