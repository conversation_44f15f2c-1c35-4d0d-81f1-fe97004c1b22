import { TableCell } from '@mui/material';
import { styled } from '@mui/system';

export const StyledTableCell: any = styled(TableCell)(({ theme }: any) => ({
  '&.MuiTableCell-head': {
    backgroundColor: 'rgb(230, 230, 230)',
    color: theme.palette.common.black,
    fontWeight: 600,
    borderRight: '1px solid #B2B2B2',
  },
  '&.MuiTableCell-body': {
    fontSize: 16,
  },
}));

export const HeadTableCell: any = styled(TableCell)(({ theme }: any) => ({
  '&.MuiTableCell-head': {
    backgroundColor: '#B2B2B2',
    color: theme.palette.common.black,
    fontWeight: 600,
    border: theme.custom.borders[0],
  },
  '&.MuiTableCell-body': {
    fontSize: 16,
    border: theme.custom.borders[0],
  },
}));
