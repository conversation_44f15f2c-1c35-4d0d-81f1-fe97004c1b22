* {
  box-sizing: border-box;
  font-family: 'Titillium Web SemiBold', 'Titillium Web', sans-serif;
}

body::-webkit-scrollbar {
  display: none;
}

.MuiInputBase-root {
  border-radius: 0;
}

.css-19kzrtu {
  height: 100%;
}

.selectedEvent {
  background-color: #ffe300;
}

.rbc-row-segment {
  display: flex;
  justify-content: center;
}

.header-title {
  font-size: 1.7rem !important;
  line-height: 1.2 !important;
  font-weight: bold !important;
}

@media (max-width: 670px) {
  .aggiorna {
    display: none;
  }
}

@media (max-width: 481px) {
  .blockMobile {
    display: block !important;
  }
}

@media (max-width: 960px) {
  .NsCustomLogin .MuiGrid-grid-sm-8 .MuiPaper-root{
    width: 50vw !important;
  }
}

.depositi-table thead tr:first-of-type th {
  background-color: rgb(200, 200, 200) !important;
}

@media (min-width: 2110px) {
  .depositi-table table thead tr:first-of-type th:first-of-type {
    width: 25% !important;
  }

  .depositi-table table thead tr:first-of-type th:nth-of-type(2) {
    width: 68% !important;
  }

  .depositi-table table thead tr:first-of-type th:last-child {
    width: 7% !important;
  }

  .depositi-table table {
    width: 100% !important;
  }
  
  .depositi-table table td, 
  .depositi-table table th {
    flex: unset !important;
    min-width: unset !important;
    max-width: unset !important;
    width: 100% !important;
  }
  
  .depositi-table table td:first-of-type, 
  .depositi-table table th:first-of-type {
    width: 50% !important;
  }
  
  .depositi-table table td:nth-of-type(2),
  .depositi-table table th:nth-of-type(2) {
    width: 60% !important;
  }
  
  .depositi-table table td:nth-of-type(3),
  .depositi-table table th:nth-of-type(3) {
    width: 100% !important;
  }
  
  .depositi-table table td:nth-of-type(4),
  .depositi-table table th:nth-of-type(4) {
    width: 70% !important;
  }
  
  .depositi-table table td:nth-of-type(5),
  .depositi-table table th:nth-of-type(5) {
    width: 60% !important;
  }
  
  .depositi-table table td:nth-of-type(6),
  .depositi-table table th:nth-of-type(6) {
    width: 60% !important;
  }
  
  .depositi-table table td:nth-of-type(7),
  .depositi-table table th:nth-of-type(7) {
    width: 100% !important;
  }
  
  .depositi-table table td:nth-of-type(8),
  .depositi-table table th:nth-of-type(8) {
    width: 180% !important;
  }
  
  .depositi-table table td:nth-of-type(9),
  .depositi-table table th:nth-of-type(9) {
    width: 150% !important;
  }
  
  .depositi-table table td:nth-of-type(10),
  .depositi-table table th:nth-of-type(10) {
    width: 160% !important;
  }
  
  .depositi-table table td:nth-of-type(11),
  .depositi-table table th:nth-of-type(11) {
    width: 140% !important;
  }
  
  .depositi-table table td:nth-of-type(12),
  .depositi-table table th:nth-of-type(12) {
    width: 120% !important;
  }
  
  .depositi-table table td:nth-of-type(13),
  .depositi-table table th:nth-of-type(13) {
    width: 165% !important;
  }
  
  .depositi-table table td:nth-of-type(14),
  .depositi-table table th:nth-of-type(14) {
    width: 180% !important;
  }
  
  .depositi-table table td:nth-of-type(15),
  .depositi-table table th:nth-of-type(15) {
    width: 110% !important;
  }
  
  .depositi-table table td:nth-of-type(16),
  .depositi-table table th:nth-of-type(16) {
    width: 140% !important;
  }
  
  .depositi-table table td:nth-of-type(17),
  .depositi-table table th:nth-of-type(17) {
    width: 120% !important;
  }
  
  .depositi-table table td:last-of-type,
  .depositi-table table th:last-of-type {
    width: 160% !important;
  }
}

@media (max-width: 768px) {
  header div.open > nav {
    min-height: 4em !important;
    flex-direction: row !important;
  }

  header nav > a {
    display: block !important;
    min-height: 2em !important;
  }

  header div.open > div.MuiBox-root > nav {
    display: flex !important;
    width: 100% !important;
    align-items: end !important;
  }
}