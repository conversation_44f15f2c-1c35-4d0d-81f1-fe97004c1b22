import { Box, Container } from '@mui/material';
import Head from 'next/head';
import { useTranslation } from 'react-i18next';
import { getStoredToken } from 'src/utils/Authentication';
import { Header } from '../components/shared/Header';
import { NsFooter } from '@netservice/astrea-react-ds';

const links = [
  { href: '#', text: 'Accessibility' },
  { href: '#', text: 'Sitemap' },
  { href: '#', text: 'Cookies' },
  { href: '#', text: 'Privacy' },
];

const logoPath = `/images/logo.png`;

export interface LayoutProps {
  children: React.ReactNode;
  padding: boolean;
}

export const DefaultLayout = ({ children, padding }: LayoutProps) => {
  const { t } = useTranslation();
  const token = getStoredToken();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {token && (
        <>
          <Head>
            <title>{t('meta.title')}</title>
            <meta name="description" content={t('meta.description')!} />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <link rel="icon" href="/favicon.ico" />
          </Head>
          <Header logo={''} entries={[]} />
        </>
      )}
      <Container
        maxWidth={false}
        sx={{
        flex: 1,
        }}
      >
        <main>{children}</main>
      </Container>
      {token && <NsFooter type="simple" links={links} logoPath={logoPath} />}
    </Box>
  );
};
