{"name": "csp-client", "version": "1.04.00", "description": "CSP Client Cassazione Penale", "contributors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "private": true, "scripts": {"dev": "relay-compiler && next dev", "build": "mkdirp __generated__ && relay-compiler && next build", "start": "next start", "lint": "next lint", "relay": "mkdirp __generated__ && relay-compiler", "prettier": "prettier --write .", "update-version": "node update-version.js"}, "dependencies": {"@azure/msal-browser": "^3.1.0", "@date-io/moment": "^2.16.1", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fontsource/titillium-web": "^4.5.9", "@fullcalendar/core": "^6.1.5", "@fullcalendar/daygrid": "^6.1.5", "@fullcalendar/react": "^6.1.5", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@mui/styles": "^5.14.3", "@mui/system": "^5.11.12", "@mui/x-date-pickers": "^6.10.2", "@netservice/astrea-react-ds": "2.8.0", "@table-library/react-table-library": "^4.1.7", "@tanstack/react-query": "^5.67.2", "@tanstack/react-table": "^8.19.3", "@tanstack/react-virtual": "^3.13.5", "@tinymce/tinymce-react": "^4.3.0", "axios": "^1.3.4", "eslint": "8.55.0", "eslint-config-next": "14.0.4", "graphql": "^16.6.0", "graphql-relay": "^0.10.0", "i18next": "^23.4.1", "i18next-browser-languagedetector": "^7.0.1", "i18next-chained-backend": "^4.2.0", "i18next-http-backend": "^2.1.1", "i18next-localstorage-backend": "^4.1.0", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "next": "14.2.16", "next-auth": "^4.24.7", "next-i18next": "^14.0.0", "notistack": "^3.0.0", "pdfjs-dist": "2.12.313", "properties-reader": "^2.3.0", "raw-loader": "^4.0.2", "react": "18.2.0", "react-big-calendar": "^1.6.9", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-i18next": "^13.0.3", "react-pdf": "5.7.2", "react-router-dom": "^6.11.2", "relay-forms": "^2.0.0", "relay-hooks": "^8.0.0", "relay-runtime": "^15.0.0", "tinymce": "^6.4.1"}, "devDependencies": {"@types/node": "18.14.6", "@types/react": "^18.2.28", "@types/react-dom": "18.0.11", "@types/react-pdf": "5.0.5", "@types/react-relay": "^14.1.6", "@types/relay-runtime": "^14.1.14", "csstype": "^3.1.1", "mkdirp": "^2.1.5", "prettier": "2.8.8", "relay-compiler": "^15.0.0", "relay-compiler-language-typescript": "^15.0.1", "relay-config": "^12.0.1", "tslib": "^2.5.0", "typescript": "4.9.5"}, "resolutions": {"pdfjs-dist": "2.12.313"}}