import { Box, Grid, MenuItem, TextField, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsDateCalendar,
  NsSelectAutocomplete,
  useNotifier
} from '@netservice/astrea-react-ds';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import useDebouncedCallback from '@restart/hooks/useDebouncedCallback';
import { FascicoliFilterProps, MenuItemProps } from '../interfaces';
import {
  convertDateFormat,
  getCollegioOptions,
  getSezioniOptions,
  tipoUdienzeOptions,
} from '../shared/Utils';

let criteriaParameters = {
  tipoUdienza: '',
  numero: '',
  anno: '',
  aula: '',
  sezioneUd: '',
  dataUdienzaDa: '',
  dataUdienzaA: '',
  oscuramentoDeskCsp: '',
  cognomeParte: '',
};

export default function FascicoliFilter({
  actionApplyFilter,
  isLoading,
}: Readonly<FascicoliFilterProps>) {
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const [anno, setAnno] = useState<string>('');
  const [numero, setNumero] = useState<string>('');
  const [collegio, setCollegio] = useState<string>('');
  const [sezione, setSezione] = useState<string>('');
  const [tipoUdienza, setTipoUdienza] = useState<string>('');
  const [dataUdienza, setDataUdienza] = useState<string>('');
  const [parte, setParte] = useState<string>('');  
  
  const debounceParti = useDebouncedCallback((parte) => {
    if (parte.length > 1) 
      getCognomiParti(parte);
    }, 500);

  const [isParteLoading, setIsParteLoading] = useState<boolean>(false);
  const [partiOptions, setPartiOptions] = useState<MenuItemProps[]>([]);

  const [collegioOptions, setCollegioOptions] = useState<MenuItemProps[]>(
    getCollegioOptions()
  );

  const [sezioniOptions, setSezioniOptions] = useState<MenuItemProps[]>(
    getSezioniOptions()
  );

  const [tipoUdienzaOptions, setTipoUdienzaOptions] = useState<MenuItemProps[]>(
    tipoUdienzeOptions()
  );

  useEffect(() => {
    debounceParti(parte);
  }, [parte]);

  const getCognomiParti = async (parteSearch: string) => {
    try {
      setIsParteLoading(true);
            
      const api = await APIWrapper;
      const response = await api.call(
        'GET',
        `ricorsi/listaParti?parte=${encodeURIComponent(parteSearch)}`
      );
      
      if (response && Array.isArray(response)) {
        const partiItems = response.map((item: any) => ({
          value: item,
          label: item
        }));
        setPartiOptions(partiItems);
      }
    } catch (err) {
      console.error('Errore nel recupero dei suggerimenti:', err);
    } finally {
      setIsParteLoading(false);
    }
  };

  const collegioItems = useMemo(
    () =>
      collegioOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [collegioOptions]
  );

  const sezioniItems = useMemo(
    () =>
      sezioniOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [sezioniOptions]
  );

  const tipoUdienzaItems = useMemo(
    () =>
      tipoUdienzaOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [tipoUdienzaOptions]
  );
  
  const cognomeParteItems = useMemo(
    () =>
      partiOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [partiOptions]
  );

  useEffect(() => {
    handlePulisciCampi();
  }, []);

  const handlePulisciCampi = () => {
    setAnno('');
    setNumero('');
    setTipoUdienza('');
    setDataUdienza('');
    setCollegio('');
    setSezione('');
    setParte('');
    setPartiOptions([]);
  };

  const handleApplicaFiltri = () => {
    if (
      (!dataUdienza || dataUdienza === '') &&
      (!anno || !numero || numero === '' || anno === '') &&
      (!parte || parte === '')
    ) {
      notify({
        type: 'warning',
        message: t('fascicoli.fascicoliFilter.dataUdienzaONumeroEAnnoRicorsoOParte'),
      });
      return;
    }
    const numeroRes = numero !== '' ? numero : null;
    const annoRes = anno !== '' ? anno : null;
    const tipoUdienzaRes = tipoUdienza !== '' ? tipoUdienza : null;
    const collegioRes = collegio !== '' ? collegio : null;
    const sezioneRes = sezione !== '' ? sezione : null;
    const dataUdienzaRes = dataUdienza !== '' ? dataUdienza : null;
    const parteRes = parte !== '' ? parte : null;

    let dataUdienzaRes2: Date | null = null;
    let validationMessage = '';
    const dataUdienzaRes1 = convertDateFormat(dataUdienzaRes);

    if (dataUdienzaRes) {
      if (dataUdienzaRes1) {
        dataUdienzaRes2 = moment(dataUdienzaRes1)
          .add(1, 'days')
          .startOf('day')
          .toDate();
      } else {
        validationMessage = 'Nessuna udienza per la data inserita';
        return;
      }
    }

    const criteriaParametersBody =  {
      numero: numeroRes,
      anno: annoRes,
      dataUdienzaDa: dataUdienzaRes1?.toISOString(),
      dataUdienzaA: dataUdienzaRes2?.toISOString(),
      tipoUdienza: tipoUdienzaRes,
      aula: collegioRes,
      sezioneUd: sezioneRes,
      cognomeParte: parteRes,      
    };

    criteriaParameters.tipoUdienza = tipoUdienzaRes ? tipoUdienzaRes : '';
    criteriaParameters.numero = numeroRes ? numeroRes : '';
    criteriaParameters.anno = annoRes ? annoRes : '';
    criteriaParameters.aula = collegioRes ? collegioRes : '';
    criteriaParameters.sezioneUd = sezioneRes ? sezioneRes : '';
    criteriaParameters.dataUdienzaDa = dataUdienzaRes ? dataUdienzaRes : '';
    criteriaParameters.cognomeParte = parteRes ? parteRes : '';

    if (!criteriaParametersBody || validationMessage != '') {
      actionApplyFilter(criteriaParametersBody, validationMessage, true);
      return;
    }
    let criteriaBody;
    if (criteriaParametersBody) {
      criteriaBody = criteriaParametersBody;
    }
    actionApplyFilter(criteriaBody, validationMessage, false); 
  };

  return (
    <Grid container sx={{ border: '1px solid #ccc' }} p={2} >
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.sezione')}
          </Typography>
        </legend>
        <NsSelectAutocomplete
          key="sezione"
          name="sezione"
          size="small"
          sx={{ width: '100%' }}
          changed={(e: any) => setSezione(e.value)}
          defaultValue={sezione}
        >
          {sezioniItems}
        </NsSelectAutocomplete>
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.dataUdienza')} <span style={{ color: 'red' }}> *</span>
          </Typography>
        </legend>
        <NsDateCalendar
          defaultValue={dataUdienza}
          key="dataUdienza"
          size="small"
          name="dataUdienza"
          sx={{ width: '100%' }}
          onChange={(e: any) => setDataUdienza(e.target.value)}
        />
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.tipoUdienza')}
          </Typography>
        </legend>

        <NsSelectAutocomplete
          key="tipoUdienza"
          name="tipoUdienza"
          defaultValue={tipoUdienza}
          size="small"
          sx={{ width: '100%' }}
          changed={(e: any) => setTipoUdienza(e.value)}
        >
          {tipoUdienzaItems}
        </NsSelectAutocomplete>
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.collegio')}
          </Typography>
        </legend>
        <NsSelectAutocomplete
          key="collegio"
          name="collegio"
          defaultValue={collegio}
          size="small"
          sx={{ width: '100%' }}
          changed={(e: any) => setCollegio(e.value)}
        >
          {collegioItems}
        </NsSelectAutocomplete>
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.numeroRicorso')}{' '}
            <span style={{ color: 'red' }}> *</span>
          </Typography>
        </legend>
        <TextField
          key="numeroRicorso"
          name="numeroRicorso"
          label=""
          type="number"
          size="small"
          value={numero}
          sx={{ width: '100%' }}
          onChange={(e) => setNumero(e.target.value)}
        />
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.annoRicorso')} <span style={{ color: 'red' }}> *</span>
          </Typography>
        </legend>

        <TextField
          key="annoRicorso"
          size="small"
          name="annoRicorso"
          label=""
          value={anno}
          type="number"
          sx={{ width: '100%' }}
          onChange={(e) => setAnno(e.target.value)}
        />
      </Box>
      <Box
        border={1}
        borderColor="grey.300"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('depositi.cognomeParte')} <span style={{ color: 'red' }}> *</span>
          </Typography>
        </legend>
        <NsSelectAutocomplete
          key="cognomeParte"
          name="cognomeParte"
          size="small"
          sx={{ minWidth: 200 }}
          noOptionsText={parte.length > 1 ? t('monitoraggio.form.nessunaParteTrovata') : ''}
          defaultValue={parte}
          filterOptions={(options, state) => options}
          loading={isParteLoading}
          inputValue={parte}
          onInputChange={(event, newInputValue) => {
            setParte(newInputValue);
          }}
        >
          {cognomeParteItems}
        </NsSelectAutocomplete>
      </Box>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          marginTop: '20px',
        }}
      >
        <NsButton
          variant="contained"
          color="secondary"
          onClick={handlePulisciCampi}
        >
          {t('fascicoli.pulisciCampi')}
        </NsButton>
        <NsButton variant="contained" onClick={handleApplicaFiltri}>
          {t('fascicoli.ricerca')}
        </NsButton>
      </div>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
      <Grid item xs={12}>
        <Typography mt={3} mb={1} variant="body2" sx={{ display: 'flex' }}>
          <span style={{ color: 'red', fontSize: '0.8rem' }}>*</span>
          {t('fascicoli.campiObbligatori')}
        </Typography>
      </Grid>
    </Grid>
  );
}