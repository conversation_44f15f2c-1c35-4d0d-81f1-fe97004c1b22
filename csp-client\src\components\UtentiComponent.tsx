import { UtentiComponentFragment$key } from '@/generated/UtentiComponentFragment.graphql';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { graphql, useFragment } from 'relay-hooks';

const fragment = graphql`
  fragment UtentiComponentFragment on Query {
    getMockPeople {
      id
      firstName
      lastName
      birthDate
    }
  }
`;

export interface UtentiComponentProps {
  data: UtentiComponentFragment$key;
}

export const UtentiComponent: React.FC<UtentiComponentProps> = ({ data }) => {
  const { t } = useTranslation();
  const { getMockPeople } = useFragment(fragment, data);
  return (
    <ul>
      {getMockPeople!.map((u) => (
        <li key={u.firstName}>{`${u.firstName} ${u.lastName} nato il ${t(
          'common.dateFormat',
          {
            date: u.birthDate,
          }
        )}`}</li>
      ))}
    </ul>
  );
};
