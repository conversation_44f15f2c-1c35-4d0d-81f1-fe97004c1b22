import { AxiosError } from 'axios';

export enum Severity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

interface LogEntry {
  timestamp: string;
  severity: Severity;
  message: string;
  stack?: string;
  context?: Record<string, any>;
  sessionId?: string;
  url?: string;
  component?: string;
}

class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private readonly MAX_LOGS = 1000;
  private readonly DEDUP_WINDOW = 5000;
  private originalConsole: Console = {} as Console;

  private constructor() {
    this.load();
    this.originalConsole = { ...console };

    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', this.onUnhandledRejection);
      window.onerror = this.onGlobalError;
    }
    this.overrideConsole();
  }

  static get() {
    return this.instance || (this.instance = new Logger());
  }

  private load() {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('errors');
        if (stored) this.logs = JSON.parse(stored);
      } catch (e) {
        this.originalConsole.warn('Failed to load stored errors:', e);
      }
    }
  }

  private save() {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('errors', JSON.stringify(this.logs));
      } catch (e) {
        this.originalConsole.warn('Failed to save errors:', e);
      }
    }
  }

private onUnhandledRejection = (event: PromiseRejectionEvent) => {
  const reason = event.reason;
  let message = 'Unhandled Rejection: ';

  if (typeof reason === 'object') {
    message += reason.message || JSON.stringify(reason);
  } else {
    message += String(reason);
  }

  this.log({
    message,
    severity: Severity.ERROR,
    context: {
      reason: typeof reason === 'object' ? {
        ...reason,
        toString: undefined
      } : reason,
      stack: reason?.stack
    }
  });
};

  private onGlobalError = (event: Event | string, source?: string, lineno?: number, colno?: number, error?: Error) => {
    const message = typeof event === 'string' ? event : event.toString();
    this.log({
      message: `Global Error: ${message}`,
      severity: Severity.ERROR,
      context: { source, lineno, colno },
      stack: error?.stack,
    });
    return false;
  };

  private overrideConsole() {
    const logLevels: (keyof Console)[] = ['error', 'warn', 'info', 'debug'];

    logLevels.forEach((level) => {
      const originalMethod = this.originalConsole[level] as (...args: any[]) => void;

      // @ts-ignore
      console[level] = (...args: any[]) => {
        originalMethod.apply(this.originalConsole, args);
        this.log({
          message: this.formatArgs(args),
          severity:
            level === 'debug'
              ? Severity.INFO
              : (Severity[level.toUpperCase() as keyof typeof Severity] ||
                  Severity.INFO),
          component: 'Console',
        });
      };
    });
  }

  private formatArgs(args: any[]): string {
    return args.map(arg => {
      if (typeof arg === 'object') {
        try {
          // Use a replacer function to handle circular references
          const seen = new WeakSet();
          return JSON.stringify(arg, (key, value) => {
            if (typeof value === 'object' && value !== null) {
              if (seen.has(value)) {
                return '[Circular Reference]';
              }
              seen.add(value);
            }
            return value;
          });
        } catch (e) {
          return '[Object conversion error]';
        }
      } else {
        return String(arg);
      }
    }).join(' ');
  }

  log({ message, severity = Severity.ERROR, context = {}, stack, component }: {
    message: string;
    severity?: Severity;
    context?: Record<string, any>;
    stack?: string;
    component?: string;
  }) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      severity,
      message,
      stack,
      context,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      component,
      sessionId: this.sessionId,
    };

    this.add(entry);
  }

  logNetworkError(error: AxiosError) {
    this.log({
      message: `Network Error: ${error.message}`,
      severity: Severity.ERROR,
      context: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        method: error.config?.method,
        requestData: error.config?.data,
        responseData: error.response?.data,
      },
      stack: error.stack,
    });
  }

  private isDuplicate(entry: LogEntry): boolean {
    const recent = this.logs.filter(log =>
      new Date(entry.timestamp).getTime() - new Date(log.timestamp).getTime() < this.DEDUP_WINDOW
    );

    return recent.some(log =>
      log.message === entry.message &&
      log.severity === entry.severity &&
      log.url === entry.url &&
      log.component === entry.component &&
      JSON.stringify(log.context) === JSON.stringify(entry.context)
    );
  }

  private add(entry: LogEntry) {
    if (this.isDuplicate(entry)) {
      if (process.env.NODE_ENV === 'development') {
        // Only log a simple message for duplicates, not the entire entry
        this.originalConsole.warn('[Logger] Skipped duplicate log entry');
      }
      return;
    }

    this.logs.push(entry);
    if (this.logs.length > this.MAX_LOGS) this.logs.shift();
    this.save();

    if (process.env.NODE_ENV === 'development') {
      // Log a simplified version of the entry to avoid circular references
      this.originalConsole.error(`[Logger] ${entry.severity}: ${entry.message}`);
    }
  }

  private get sessionId(): string {
    if (typeof window === 'undefined') return 'server';

    let id = sessionStorage.getItem('sessionId');
    if (!id) {
      id = Math.random().toString(36).substring(2);
      sessionStorage.setItem('sessionId', id);
    }
    return id;
  }

  getLogs(): LogEntry[] {
    return this.logs;
  }

  clear() {
    this.logs = [];
    this.save();
  }
}

export const errorLogger = Logger.get();