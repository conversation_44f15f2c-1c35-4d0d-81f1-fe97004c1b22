import { NextApiRequest, NextApiResponse } from 'next';

const conf = {
  urlServizi: process.env.SERVIZI_CSP_URL,
  signAuthAzure: process.env.DIGITAL_SIGNATURE_AUTHORITY_AZURE,
  signClientId: process.env.DIGITAL_SIGNATURE_CLIENT_ID_AZURE,
  signRedirect: process.env.DIGITAL_SIGNATURE_REDIRECT_URI_AZURE,
  signScope: process.env.DIGITAL_SIGNATURE_SCOPE_AZURE,
  enabledFirmaOtp: process.env.ENABLE_FIRMA_OTP,
};

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  res.status(200).json(conf);
}
