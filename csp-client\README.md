# Frontendista Starter Pack

## Features

- NextJs 13
- React 18
- Relay 14 + relay-hooks
- Mu<PERSON> 5
- Localizzazione con next-i18next
- @ns/nsds-react con relay-forms

## Login

- Utente test:test
- https://dev2-docker-sigma/auth ed entri con admin/admin

## Import di componenti da libreria nsds-react

- esempio di import di un componente da nsds-react

import { NsButton } from '@netservice/astrea-react-ds';

- si possono controllare i componenti disponibili nel file index.d.ts della libreria
- l'implementazione dei componenti è visibile su http://design.netserv.it/nsds-react
- per aggiungere un componente a nsds-react, aggiungere il componente in src/components e aggiungere l'expoprt del componente in src/index.d.ts
- va creato inoltre il file stories per il componente in src/stories/components
- si può consultare la board di nsds-react su https://gitpa.netserv.it/libraries/nsds-react/-/boards
- se il componente che si desidera non è presente in nsds-react, si può creare richiedere l'aprtura di una card su https://gitpa.netserv.it/libraries/nsds-react/-/boards
- per creare un nuovo componente bisognerà creare un nuovo branch e una nuova merge request partendo dal branch 'develop'. Una volta finito lo sviluppo del component si può richiedere la merge request dopo aver spostato la card in to TEST. Una volta approvata la merge request, il componente sarà disponibile in nsds-react
