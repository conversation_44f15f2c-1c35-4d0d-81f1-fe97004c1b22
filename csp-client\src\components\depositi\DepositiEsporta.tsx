import { Box, Grid, MenuItem, TextField, Typography } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsSelectAutocomplete,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { useEffect, useMemo, useState } from 'react';
import { getSiglaSezione } from 'src/utils/Authentication';
import { getSezioniOptions } from '../shared/Utils';
import { DepositiEsportaProps } from '../interfaces';
import { useTranslation } from 'react-i18next';
import { useForm } from 'relay-forms';
import APIWrapper from 'src/utils/APIWrapper';

const DepositiEsporta = ({ closeModal }: DepositiEsportaProps) => {
  const [sezione, setSezione] = useState<string>('');
  const [dataDa, setDataDa] = useState<string>('');
  const [dataA, setDataA] = useState<string>('');
  const [meseOk, setMeseOk] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { notify } = useNotifier();
  const sezioniOptions = getSiglaSezione();
  const { t } = useTranslation();

  const criteriaParameters = {
    tipoProvvedimento: '',
    tipoUdienza: '',
    numeroRicorso: '',
    annoRicorso: '',
    aula: '',
    sezione: '',
    OscuramentoDeskCsp: '',
    dataDeposito: '',
    dataUdienza: '',
    cfMittente: '',
    stato: '',
    anomalia: '',
    fullName: '',
  };

  useEffect(() => {
    if (sezioniOptions != 'CE') {
      criteriaParameters.sezione = sezioniOptions;
      setSezione(sezioniOptions);
    }
  }, [sezioniOptions]);

  useEffect(() => {
    if (!dataA || !dataDa) return;
    const diff = new Date(dataA).getTime() - new Date(dataDa).getTime();
    if (diff < 0) {
      setMeseOk(false);
      return;
    }
    const days = Math.round(diff / (1000 * 3600 * 24));
    setMeseOk(days < 31);
  }, [dataDa, dataA]);

  const sezioniOptionsCE = getSezioniOptions();

  const sezioniMemo = useMemo(
    () => (
      <MenuItem key={sezioniOptions} value={sezioniOptions}>
        {sezioniOptions}
      </MenuItem>
    ),
    [sezioniOptions]
  );

  const sezioniMemoCE = useMemo(
    () =>
      sezioniOptionsCE.map((sezione, i) => (
        <MenuItem key={i} value={sezione.value}>
          {sezione.label}
        </MenuItem>
      )),
    [sezioniOptionsCE]
  );

  const esportaFile = async (criteriaParameters: any) => {
    try {
      setIsLoading(true);
      const api = await APIWrapper;
      await api.download('depositi/esporta', { criteriaParameters });      
      notify({
        message: t('depositi.fileScaricato'),
        type: 'success',
      });
      closeModal();
    } catch (error) {
      console.error('Error exporting file:', error);
      notify({
        message: t('depositi.scaricamentoFallito'),
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const { submit } = useForm<any>({
    onSubmit: () => {
      esportaFile({
        sezioneUdienza: sezione,
        dataDepositoDa: new Date(dataDa).setHours(0, 0, 59, 0),
        dataDepositoA: new Date(dataA).setHours(23, 59, 59, 0),
      });
    },
  });

  return (
    <>
      <Grid container>
        <Grid item xs={4} mt={2}>
          <Typography mb={1}>{t('depositi.sezione')}</Typography>
          <NsSelectAutocomplete
            name="sezione"
            defaultValue={sezione}
            size="small"
            sx={{ width: '250px' }}
            changed={(e: any) => setSezione(e.value)}
          >
            {sezioniOptions === 'CE' ? sezioniMemoCE : sezioniMemo}
          </NsSelectAutocomplete>
        </Grid>

        <Grid item xs={4} mt={2}>
          <Typography mb={1}>{t('depositi.dataDa')}</Typography>
          <TextField
            size="small"
            type="date"
            value={dataDa}
            label=""
            name="dataDa"
            sx={{ minWidth: 250 }}
            onChange={(e) => setDataDa(e.target.value)}
            required
          />
        </Grid>

        <Grid item xs={4} mt={2}>
          <Typography mb={1}>{t('depositi.dataA')}</Typography>
          <TextField
            size="small"
            type="date"
            value={dataA}
            label=""
            name="dataA"
            sx={{ minWidth: 250 }}
            onChange={(e) => setDataA(e.target.value)}
            required
          />
        </Grid>

        <Grid item container justifyContent="space-between" xs={12} mt={5}>
          <NsButton onClick={closeModal}>{t('buttonsLabel.annulla')}</NsButton>
          <Box display="flex" flexDirection="column">
            {!sezione && (
              <Typography variant="h3">
                {t('depositi.erroreSezione')}
              </Typography>
            )}
            {!meseOk && (
              <Typography variant="h3">{t('depositi.erroreDate')}</Typography>
            )}
          </Box>
          <NsButton
            variant="contained"
            onClick={submit}
            disabled={!meseOk || !sezione}
          >
            {t('common.esporta')}
          </NsButton>
        </Grid>
      </Grid>
      <NsFullPageSpinner isOpen={isLoading} value={100} />
    </>
  );
};

export default DepositiEsporta;
