// @ts-nocheck

import { DefaultButtons, useNotifier } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { storeToken } from 'src/utils/Authentication';
import APIWrapper from '../../utils/APIWrapper';
import { UserLoginProps } from '../interfaces';
import { NsFullPageSpinner } from '@netservice/astrea-react-ds';
import React from 'react';
import { NsLogin as CustomLogin } from '@netservice/astrea-react-ds';

export default function Login() {
  const { notify } = useNotifier();
  const router = useRouter();
  const [loading, setLoading] = React.useState(false);

  const logo = `${
    process.env.NEXT_PUBLIC_BASE_PATH || ''
  }/images/logo-repubblica-giustizia-colored.png`;

  const empty = `${process.env.NEXT_PUBLIC_BASE_PATH || ''}/images/Empty.png`;

  const handleFormSubmit = (data: any) => {
    let userDetails: UserLoginProps = {
      userName: data.username,
      pwd: data.password,
    };
    getToken(userDetails);
  };

  const getToken = async (userDetails: UserLoginProps) => {
    if (!validateForm(userDetails)) return;
    setLoading(true);
    try {
      const api = await APIWrapper;
      const token = await api.call('POST', `auth`, userDetails);
      await storeToken(token);
      router.push('/depositi');
    } catch (error: any) {
      console.error('Error occurred while fetching token:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (userDetails: UserLoginProps) => {
    if (!userDetails.pwd || !userDetails.userName) {
      notify({ type: 'error', message: 'Missing fields' });
      return false;
    }
    return true;
  };

  const headerTitle = {
    bold: 'CSP Client Corte Suprema di Cassazione',
    thin: '',
    subtitle: '',
  };

  return (
    <div className='NsCustomLogin'>
      {loading && <NsFullPageSpinner isOpen={loading} value={100} />}
      <CustomLogin
        headerTitle={headerTitle}
        gradient="linear-gradient(-240.64224645720873deg, rgba(48, 138, 125, 0.99)  1.9191447712979693e-14%, rgba(48, 138, 125, 0.7), #0c4b50 100% )"
        logoSrc={logo}
        imagePath={empty}
        title1="CSP Client Corte Suprema di Cassazione"
        title2={process.env.PACKAGE_VERSION}
        type="form"
        handleFormSubmit={handleFormSubmit}
        cardWidth='600px'
        buttonsSlot={
          <DefaultButtons
            submitText="Accedi"
          />
        }
      />
    </div>
  );
}
