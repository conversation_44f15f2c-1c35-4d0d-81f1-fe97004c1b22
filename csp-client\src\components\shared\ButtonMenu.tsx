import { Box, Menu, MenuItem, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useState } from 'react';

interface ButtonProps {
  name: string;
  menuNames: string[];
  endIcon: any;
}

export default function ButtonMenu({ name, menuNames, endIcon }: ButtonProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  return (
    <Box>
      <NsButton
        variant="contained"
        color="primary"
        endIcon={endIcon}
        onClick={handleClick}
      >
        <Typography variant="h5">{name}</Typography>
      </NsButton>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {menuNames?.map((name, i) => (
          <MenuItem key={i} onClick={handleClose}>
            {name}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
}
