Guide e approfondimenti sulle tecnologie del progetto
=====================================================

## React

* :mortar_board:  [React Tutorial](https://reactjs.org/tutorial/tutorial.html) - Il tutorial ufficiale di React. Spiega il vecchio paradigma delle classi Javascript invece dei componenti funzionali, quindi bisogna tenere presente che nello sviluppo di applicazioni ora si usano esclusivamente i secondi, salvo casi eccezionali.

* :warning: [Perchè l'immutabilità è importante](https://reactjs.org/tutorial/tutorial.html#why-immutability-is-important) - Fa parte del tutorial precedente, ma è uno di quei concetti che vanno ribaditi :smiley:

* :mortar_board:  [Introduzione agli hook React](https://reactjs.org/docs/hooks-intro.html) - Spiegazione degli Hooks, che rappresentano il nuovo approccio alla gestione della logica applicativa (principalmente lo stato e i side-effects) nei componenti funzionali.

* :mortar_board: [Learn ReactJs](https://www.youtube.com/watch?v=Dorf8i6lCuk) - Corso che rispecchia il nostro modo di sviluppare applicazioni.


## Liquibase
**Liquibase** è open-source database-independent library per la gestione e l'applicazione delle modifiche allo schema del database, soprattutto in un ambiente di sviluppo software agile

* [Documentazione ufficiale di Liquibase](https://www.liquibase.com/)


## Azure
firma di accesso condiviso in **Azure**

* [Documentazione di Azure](https://www.npmjs.com/package/@azure/msal-browser/)
