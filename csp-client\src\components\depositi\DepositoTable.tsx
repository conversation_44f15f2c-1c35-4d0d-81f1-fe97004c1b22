import { NsFullPageSpinner, useNotifier } from '@netservice/astrea-react-ds';
import { FetchDataOptions, NsDataGridVirtualizedInfiniteScrolling } from 'src/components/shared/NsDataGridVirtualizedInfiniteScrolling';
import { useCallback, useEffect, useRef, useState } from 'react';
// @ts-ignore
import debounce from 'lodash/debounce';
import { useTranslation } from 'react-i18next';
import { getSiglaSezione } from 'src/utils/Authentication';
import APIWrapper from '../../utils/APIWrapper';
import StatoProvvedimentoEnum from '../enum/StatoProvvedimentoEnum';
import TipologiaProvvedimentoEnum from '../enum/TipologiaProvvedimentoEnum';
import { DepositoTableProps } from '../interfaces';
import SideModal from '../shared/SideModal';
import { COLUMN_MAPPINGS, getDefaultBackendSorting } from './constants/depositiSorting';
import { formatDate } from '../shared/Utils';
import DetailRiuniti from '../fascicolo/DetailRiuniti';
import DialogModal from '../shared/DialogModal';
import useDepositiColumns from './hooks/useDepositiColumns';
import useDepositiData from './hooks/useDepositiData';
import useModalState from './hooks/useModalState';
import Box from '@mui/material/Box';

export default function DepositoTable({
  filteredRequestBody,
  loadDepositi,
  setParentLoading,
  selectedRows,
  setSelectedRows
}: Readonly<DepositoTableProps>) {
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const isOpenModal = useRef(false);
  const initialCallMade = useRef(false);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [isModalDataLoading, setIsModalDataLoading] = useState(false);
  const [requestBodyHash, setRequestBodyHash] = useState('');

  const { openDrawer, setOpenDrawer, data, setData } = useDepositiData();
  const { modalProps, setModalProps } = useModalState(() => {
    setTimeout(() => { isOpenModal.current = false }, 100);
  });

  const sezione = getSiglaSezione();
  const criteriaParameters = sezione === 'CE' ? {} : { sezioneUdienza: sezione };

  // Create a hash of the request body to detect actual changes
  const createRequestBodyHash = useCallback((body: any) => {
    if (!body) return '';
    return JSON.stringify({
      pageInfo: body.pageInfo,
      criteriaParameters: body.criteriaParameters
    });
  }, []);


  useEffect(() => {
    if (filteredRequestBody) {
      const newHash = createRequestBodyHash(filteredRequestBody);

      // Only trigger a new fetch if the request body has actually changed by updating the key
      if (newHash !== requestBodyHash) {
        setRequestBodyHash(newHash);
        setRefreshCounter(prev => prev + 1); // Force refresh if filters change

        // Clear selected rows when filter changes to prevent rows from previous filters remaining selected
        if (setSelectedRows) {
          setSelectedRows([]);
        }
      }
    }
  }, [filteredRequestBody, createRequestBodyHash, requestBodyHash, setSelectedRows]);

  const mapResponseData = useCallback((responseData: any[]) => {
    if (!responseData) return [];

    return responseData.map((item: any) => {
      let tipo = '';
      switch (item.tipo) {
        case TipologiaProvvedimentoEnum.SENTENZA: tipo = 'Sentenza'; break;
        case TipologiaProvvedimentoEnum.ORDINANZA: tipo = 'Ordinanza'; break;
        case TipologiaProvvedimentoEnum.MINUTASENTENZA: tipo = 'Minuta di Sentenza'; break;
        case TipologiaProvvedimentoEnum.MINUTAORDINANZA: tipo = 'Minuta di Ordinanza'; break;
      }

      let stato = '';
      let dataLavorazione = '';

      if (item.stato === StatoProvvedimentoEnum.ACCETTATO) {
        stato = (item.tipo === TipologiaProvvedimentoEnum.MINUTASENTENZA ||
          item.tipo === TipologiaProvvedimentoEnum.MINUTAORDINANZA)
          ? 'Accettato' : 'Pubblicato';
        dataLavorazione = item.dataAccettazione;
      } else if (item.stato === StatoProvvedimentoEnum.RIFIUTATO) {
        stato = 'Rifiutato';
        dataLavorazione = item.dataRifiuto;
      } else if (item.stato === StatoProvvedimentoEnum.NEW) {
        stato = 'Da accettare';
      } else if (item.stato === StatoProvvedimentoEnum.ERROREPUBBLICAZIONE) {
        stato = 'Errore di pubblicazione';
      }
      const anomalie = item.anomalie.split('-');
      const isDepositoLavoratoDaSic = anomalie.length > 3 && anomalie[3] === '1';

      return {
        id: item.idCat.toString(),
        dataudienza: item.dataUdienza ? formatDate(item.dataUdienza, 'DD/MM/YYYY') : '',
        sezioneUdienza: item.sezioneUdienza,
        tipo: item.tipoUdienza,
        collegio: item?.aula,
        numRaccGenerale: item?.numRaccGenerale ?? '',
        dataAccettazione: item.dataAccettazione,
        nrg: `${item.numeroRicorso}/${item.annoRicorso}`,
        depositante: item.depositante,
        atto: tipo,
        presidenteCollegio: item.presCollegio,
        data: formatDate(item.dataDeposito, 'DD/MM/YYYY HH:mm'),
        oscuramentoDeskCsp: item.oscuramentoDeskCsp,
        oscuramentoSic: item.oscuramentoSic,
        dePlano: item.deplano,
        inCaricoDa: item.utenteInCarico,
        statoDeposito: stato,
        dataLavorazione: dataLavorazione ? formatDate(dataLavorazione, 'DD/MM/YYYY HH:mm') : '',
        diagnosi: item.anomalie.split('-'),
        ricercaRiunitiView: item.ricercaRiunitiView,
        isDepositoLavoratoDaSic: isDepositoLavoratoDaSic,
      };
    });
  }, []);

  // Debounced version of the API call to prevent multiple rapid calls
  const debouncedApiCall = useCallback(
    debounce(async (api: any, requestBody: any, callback: (response: any) => void) => {
      try {
        const response = await api.call('POST', 'depositi', requestBody);
        callback(response);
      } catch (err) {
        console.error('Error in debounced API call:', err);
        callback(null);
      }
    }, 300),
    []
  );

  const fetchData = useCallback(async (options: FetchDataOptions<any>): Promise<any> => {
    const { pageParam = 0, fetchSize, sorting } = options;

    if (pageParam === 0) {
      initialCallMade.current = true;
    }

    let orderObjects = undefined;
    if (sorting && sorting.length > 0) {
      const sortingConfig = sorting.map((sort: { id: any; desc: any; }) => {
        const { id, desc } = sort;
        const propertyName = COLUMN_MAPPINGS[id] || id;

        return {
          propertyName: propertyName,
          asc: !desc
        };
      });

      orderObjects = sortingConfig;
    } else {
      // Usa l'ordinamento di default centralizzato
      orderObjects = getDefaultBackendSorting();
    }

    const requestBody = {
      pageInfo: {
        pageNumber: pageParam,
        pageSize: fetchSize,
        orderList: orderObjects,
      },
      criteriaParameters: {
        ...criteriaParameters,
        ...(filteredRequestBody?.criteriaParameters || {}),
      },
    };

    try {
      const api = await APIWrapper;

      // For page 0 (initial load), use the debounced version to prevent multiple calls
      if (pageParam === 0) {
        return new Promise((resolve) => {
          debouncedApiCall(api, requestBody, (response: any) => {
            if (!response) {
              resolve({
                data: [],
                meta: { totalRowCount: 0 }
              });
              return;
            }

            processResponse(response, pageParam, fetchSize, resolve);
          });
        });
      }

      // For subsequent pages, make the call directly
      const response = await api.call('POST', 'depositi', requestBody);
      return processResponse(response, pageParam, fetchSize);
    } catch (err) {

      console.error('Error fetching data:', err);
      if (pageParam === 0) {
        setTimeout(() => {
          setIsFilterLoading(false);
          if (setParentLoading) {
            setParentLoading(false);
          }
        }, 0);
      }
      return {
        data: [],
        meta: {
          totalRowCount: 0,
        },
      };
    }
  }, [filteredRequestBody, criteriaParameters, mapResponseData, debouncedApiCall, setParentLoading]); // Removed shouldFetchData

  // Helper function to process API response
  const processResponse = useCallback((response: any, pageParam: number, _fetchSize: number, resolvePromise?: (value: any) => void): any => {
    const totalCount = response?.pageInfo?.totRecordsCount || 0;

    const mappedData = mapResponseData(response?.content);

    if (pageParam === 0) {
      setTimeout(() => {
        setIsFilterLoading(false);

        if (setParentLoading) {
          setParentLoading(false);
        }
      }, 0);
    }

    const result = {
      data: mappedData,
      meta: {
        totalRowCount: totalCount,
      },
    };

    if (resolvePromise) {
      resolvePromise(result);
      return;
    }
    return result;
  }, [mapResponseData]);

  const handleModal = async (param: string, data?: any) => {
    if (param === 'riuniti' && data.idRicUdienza) {
      const content = <DetailRiuniti idRicUdienza={data.idRicUdienza} />;
      const title = t('fascicoli.fascicoliTable.visualizzazioneRiuniti');
      setModalProps({ ...modalProps, content, isOpen: true, title });
      isOpenModal.current = true;
    }
  };

  const handleSlideModal = async (row: any) => {
    if (row.isDepositoLavoratoDaSic) {
      notify({
        type: 'error',
        message: t('depositi.erroreProvvedimentoLavoratoDaSIC'),
      });
      return;
    }
    if (!isOpenModal.current) {
      try {
        setIsModalDataLoading(true);
        const api = await APIWrapper;

        // Fetch all required data concurrently
        const [esitiData, elabDepData, depositoData, contenutiData] = await Promise.all([
          api.call('GET', `depositi/esiti?idDeposito=${row.id}`),
          api.call('GET', `elaborazionedeposito/byIdDeposito?idDeposito=${row.id}`),
          api.call('GET', `depositi?idDeposito=${row.id}`),
          api.call('GET', `depositi/contenuti?idDeposito=${row.id}`)
        ]);

        const aggregatedData = {
          id: row.id,
          nrg: row.nrg,
          dataAccettazione: row.dataAccettazione,
          dePlano: row.dePlano,
          sideBarDetails: [...esitiData],
          elaborazioneDeposito: elabDepData,
          deposito: depositoData,
          contenuti: contenutiData,
          data: row.data
        };

        setData(aggregatedData);
        setOpenDrawer(true);

      } catch (err) {
        console.error('Error fetching side modal data:', err);
      } finally {
        setIsModalDataLoading(false);
      }
    }
  };

  const { groupedColumns, initialSorting } = useDepositiColumns(t, handleModal, handleSlideModal, selectedRows, setSelectedRows);

  const refreshData = useCallback(() => {
    // Increment counter to trigger remount via key prop
    setRefreshCounter(prev => prev + 1);
  }, []);

  return (
    <Box
      style={{
        width: '100%'
      }}
      className='depositi-table'
    >
      <NsFullPageSpinner isOpen={isFilterLoading || isModalDataLoading} value={0} variant="indeterminate" />
      <DialogModal {...modalProps} />
      <SideModal
        data={data}
        openDrawer={openDrawer}
        closeDrawer={() => setOpenDrawer(false)}
        refreshTable={refreshData}
      />
      <NsDataGridVirtualizedInfiniteScrolling
        columns={groupedColumns}
        fetchData={fetchData}
        fetchSize={filteredRequestBody?.pageSize?.pageSize || 10}
        containerHeight="600px"
        estimatedRowHeight={10}
        initialSorting={initialSorting}
        enableColumnGrouping={true}
        filteredRequestBody={filteredRequestBody}
        key={`depositi-table-${loadDepositi}-${refreshCounter}`} // Forza il ricaricamento quando loadDepositi o refreshCounter cambia
      />
    </Box>
  );
}

function notify() {
  throw new Error('Function not implemented.');
}
