import { Box, Button, Typography } from '@mui/material';
import { useNotifier } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { getUserData } from 'src/utils/Authentication';

interface ApriCaricamentoProps {
  data?: any;
  closeModal: any;
  idUtente?: number;
  closeDrawer?: () => void;
  refreshData?: () => void;
}

export default function ApriCaricamento({
  data,
  closeModal,
  idUtente,
  closeDrawer,
  refreshData,
}: ApriCaricamentoProps) {
  const buttonStyle = {
    fontSize: '1em',
    bgcolor: '#CCE2DF',
    color: '#308A7D',
    paddingRight: '1em',
    paddingLeft: '1em',
  };

  const { t } = useTranslation();
  const { notify } = useNotifier();
  const router = useRouter();

  const [isAlreadyAssigned, setIsAlreadyAssigned] = useState<boolean>();
  const handleConferma = async () => {
    try {
      const api = await APIWrapper;
      const message = await api.call(
        'GET',
        `depositi/presaInCarico?idDeposito=${data.id}`
      );
      notify({
        type: 'success',
        message: t('depositi.apriCaricamento.depositoPresoInCarico'),
      });
      if (closeDrawer) {
        closeDrawer();
      }

      if (refreshData) {
        refreshData();
      }
      closeModal();
      router.push(`/depositi/${data.id}`);
      //window.location.reload();
    } catch (err: any) {
      console.log('error', err);
    }
  };
  useEffect(() => {
    if (idUtente && idUtente != getUserData().idUtente) {
      setIsAlreadyAssigned(true);
    }
  }, [data]);

  return (
    <Box p={3}>
      <Box mb={3}>
        {isAlreadyAssigned ? (
          <Typography variant="h3" sx={{ marginTop: 2, marginLeft: 2 }}>
            {`Attualmente il deposito del fascicolo ${data.nrg} è in carico ad un altro utente. Confermi di volerlo prendere in carico?`}
          </Typography>
        ) : (
          <Typography variant="h4" sx={{ marginTop: 2, marginLeft: 2 }}>
            {t('depositi.apriCaricamento.confermandoPresaInCaricoDeposito')}
          </Typography>
        )}
      </Box>
      <Box display="flex" justifyContent="space-between" mt={4}>
        <Button
          size="small"
          onClick={() => closeModal()}
          sx={{ ...buttonStyle }}
        >
          {t('buttonsLabel.annulla')}
        </Button>
        <Button variant="contained" onClick={handleConferma} color="primary">
          {t('buttonsLabel.conferma')}
        </Button>
      </Box>
    </Box>
  );
}
