/**
 * Configurazione centralizzata per l'ordinamento dei depositi
 * Questo file contiene tutte le configurazioni di ordinamento per evitare duplicazioni
 */

// Tipo per l'ordinamento backend (API)
export interface BackendSortConfig {
  propertyName: string;
  asc: boolean;
}

// Tipo per l'ordinamento frontend (TanStack Table)
export interface FrontendSortConfig {
  id: string;
  desc: boolean;
}

// Mapping tra ID frontend e nomi proprietà backend
export const COLUMN_MAPPINGS: Record<string, string> = {
  'id': 'idCat',
  'dataudienza': 'dataUdienza',
  'sezioneUdienza': 'sezioneUdienza',
  'tipo': 'tipoUdienza',
  'collegio': 'aula',
  'nrg': 'numeroRicorso',
  'depositante': 'depositante',
  'atto': 'tipo',
  'presidenteCollegio': 'presCollegio',
  'data': 'dataDeposito',
  'oscuramentoSic': 'oscuramentoSic',
  'oscuramentoDeskCsp': 'oscuramentoDeskCsp',
  'dePlano': 'deplano',
  'inCaricoDa': 'utenteInCarico',
  'statoDeposito': 'stato',
  'dataLavorazione': 'dataAccettazione',
  'numRaccGenerale': 'numRaccGenerale'
};

// Configurazione di ordinamento di default
export const DEFAULT_DEPOSITI_SORTING = {
  // Ordinamento per il backend (API calls)
  backend: [
    { propertyName: 'dataUdienza', asc: false },      // 1° criterio: Data Udienza alfabetico A-Z
    { propertyName: 'numeroRicorso', asc: false },     // 2° criterio: Numero Ricorso più recente prima
    { propertyName: 'depositante', asc: true },   // 3° criterio: Depositante più alto prima
    { propertyName: 'dataDeposito', asc: false },    // 4° criterio: Data Deposito più recente prima
  ] as BackendSortConfig[],

  // Ordinamento per il frontend (TanStack Table)
  frontend: [
    { id: 'dataudienza', desc: true },               // 1° criterio: Data Udienza più recente prima
    { id: 'nrg', desc: true },                       // 2° criterio: NRG più alto prima
    { id: 'depositante', desc: false },              // 3° criterio: Depositante alfabetico A-Z
    { id: 'data', desc: true },                      // 4° criterio: Data Deposito più recente prima
  ] as FrontendSortConfig[]
};

// Ordinamento di fallback per situazioni specifiche (filtri, export, etc.)
export const FALLBACK_SORTING = {
  propertyName: 'idCat',
  asc: false
} as BackendSortConfig;

/**
 * Converte l'ordinamento frontend in formato backend
 * @param frontendSorting Array di configurazioni frontend
 * @returns Array di configurazioni backend
 */
export function convertFrontendToBackend(frontendSorting: FrontendSortConfig[]): BackendSortConfig[] {
  return frontendSorting.map(sort => {
    const propertyName = COLUMN_MAPPINGS[sort.id] || sort.id;
    return {
      propertyName,
      asc: !sort.desc
    };
  });
}

/**
 * Converte l'ordinamento backend in formato frontend
 * @param backendSorting Array di configurazioni backend
 * @returns Array di configurazioni frontend
 */
export function convertBackendToFrontend(backendSorting: BackendSortConfig[]): FrontendSortConfig[] {
  // Crea un mapping inverso per trovare l'ID frontend dal nome proprietà backend
  const reverseMapping = Object.entries(COLUMN_MAPPINGS).reduce((acc, [frontendId, backendProp]) => {
    acc[backendProp] = frontendId;
    return acc;
  }, {} as Record<string, string>);

  return backendSorting.map(sort => {
    const id = reverseMapping[sort.propertyName] || sort.propertyName;
    return {
      id,
      desc: !sort.asc
    };
  });
}

/**
 * Ottiene l'ordinamento di default per il backend
 */
export function getDefaultBackendSorting(): BackendSortConfig[] {
  return [...DEFAULT_DEPOSITI_SORTING.backend];
}

/**
 * Ottiene l'ordinamento di default per il frontend
 */
export function getDefaultFrontendSorting(): FrontendSortConfig[] {
  return [...DEFAULT_DEPOSITI_SORTING.frontend];
}

/**
 * Ottiene l'ordinamento di fallback
 */
export function getFallbackSorting(): BackendSortConfig {
  return { ...FALLBACK_SORTING };
}
