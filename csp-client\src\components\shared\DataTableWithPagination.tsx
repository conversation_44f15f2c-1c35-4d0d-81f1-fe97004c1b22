import {
  Box,
  Grid,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { HeadTableCell, StyledTableCell } from './TableCells';
import { DataTableWithPaginationProps, PageInfoProps } from "../interfaces";
import React from 'react';


export default function DataTableWithPagination({
  rows,
  columns,
  action,
  actionChangePage,
  pageInfoServer,
  maxHeight = 'unset',
  addPaginate = false,
  sorting,
  createSortHandler,
  resultMessage = null,
}: Readonly<DataTableWithPaginationProps>) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [rowsPerPage, setRowsPerPage] = useState(pageInfoServer?.pageSize || 25);
  const [page, setPage] = useState(0);

  const isSorted = (id: string) => {
    return pageInfoServer.order?.propertyName === id || 
      pageInfoServer.orderList?.some(order => order.propertyName === id);
  }

  const isAscDirection = (id: string) => {
    if(pageInfoServer.order?.propertyName === id)
      return pageInfoServer.order.asc
    else 
      return pageInfoServer.orderList?.find((order) => order.propertyName === id)?.asc
  }
    
  const sortDirection = (id: string) => isSorted(id) && isAscDirection(id) ? 'asc' : 'desc';

  const updatePagination = (pageNumber: number, pageSize: number) => {
    if(actionChangePage  instanceof Function) {
      const paginationInfo: PageInfoProps = {
        pageNumber,
        pageSize,
      }
      actionChangePage(paginationInfo);
    }
  }

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
    updatePagination(newPage, rowsPerPage);    
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newRowsPerPage = +event.target.value;
    const newPage = 0;
    setRowsPerPage(newRowsPerPage);
    setPage(newPage);
    updatePagination(newPage, newRowsPerPage);
  };

  const baseColumns = columns.filter((column: any) => !column?.main);
  const mainHeaders = columns.filter((column: any) => column?.main);

  return (
    <>
      {rows.length > 0 ? (
        <TableContainer
          sx={{
            border: theme.custom.borders[0],
            borderBottom: 'unset',
            maxHeight,
          }}
          component={Paper}
        >
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow className={'ns-custom-tr'}>
                {mainHeaders.map((column: any, i: number) => {
                  return column.main ? (
                    <HeadTableCell colSpan={column.size} key={i}>
                      {' '}
                      {column.label}{' '}
                    </HeadTableCell>
                  ) : null;
                })}
              </TableRow>
              {sorting && createSortHandler ? (
                <TableRow className={'ns-custom-tr'}>
                  {baseColumns.map((column: any, i: number) => {
                    return (
                      <StyledTableCell
                        key={i}
                        onClick={() => {
                          if (column.sortable !== false && column.id !== 'diagnosi') {
                            createSortHandler(column.id);
                          }
                        }}
                        sx={{
                          cursor:
                            column.sortable !== false && column.id !== 'diagnosi' ? 'pointer' : 'default',
                        }}
                      >
                        {column.sortable !== false && column.id !== 'diagnosi' ? (
                          <TableSortLabel
                            active={isSorted(column.id)}
                            direction={sortDirection(column.id)}
                          >
                            {column.renderHeadCell
                              ? column.renderHeadCell(column.label, rows)
                              : column.label}{' '}
                          </TableSortLabel>
                        ) : (
                          <Box>
                            {column.renderHeadCell
                              ? column.renderHeadCell(column.label, rows)
                              : column.label}{' '}
                          </Box>
                        )}
                      </StyledTableCell>
                    );
                  })}
                </TableRow>
              ) : (
                <TableRow className={'ns-custom-tr'}>
                  {baseColumns.map((column: any, i: number) => {
                    return (
                      <StyledTableCell
                        style={{ minWidth: column.minWidth }}
                        key={i}
                      >
                        {column.renderHeadCell
                          ? column.renderHeadCell(column.label, rows)
                          : column.label}{' '}
                      </StyledTableCell>
                    );
                  })}
                </TableRow>
              )}
            </TableHead>
            <TableBody>
              {rows.map((row: any, i: number) => {
                  const currentId = i + 10 * page;
                  return (
                    <TableRow className={'ns-custom-tr'}
                      hover
                      role="checkbox"
                      tabIndex={-1}
                      key={i}
                      sx={action != null ? { cursor: 'pointer' } : null}
                      onClick={
                        action != null ? () => action(row) : () => null
                      }
                    >
                      {baseColumns.map((column: any, index: number) => {
                        const value = row[column.id];
                        if (column.render) {
                          return React.cloneElement(
                            column.render(column, row, currentId),
                            { key: `cell-${i}-${index}` }
                          );
                        }
                        return (
                          <TableCell
                            key={`cell-${i}-${index}`}
                            align={column.align}
                            sx={{
                              border: theme.custom.borders[0],
                              background: row?.color ? row.color : '',
                              color: row?.color == 'black' ? 'white' : '',
                            }}
                          >
                            {value}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Grid width="100%">
          <Typography mt={3} variant="h3">
            {resultMessage || t('fascicoli.nessunRisultato')}
          </Typography>
        </Grid>
      )}
      {addPaginate && (
        <TablePagination
          rowsPerPageOptions={[10, 25, 50]}
          component="div"
          count={pageInfoServer.totRecordsCount ?? 0}
          rowsPerPage={pageInfoServer.pageSize ?? 25}
          page={pageInfoServer.pageNumber ?? 0}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Righe per pagina"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} di ${count}`
          }
        />
      )}
    </>
  );
}
