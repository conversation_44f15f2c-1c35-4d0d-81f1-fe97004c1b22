import { NsFormWrapper } from '@netservice/astrea-react-ds';
import { DefaultLayout, LayoutProps } from './DefaultLayout';

/**
 * Layout di default per i form, wrappa già il contenuto della pagina in un NsFormWrapper  in modo da poter usare gli hook di validazione all'interno
 */
export const FormLayout = ({ children }: LayoutProps) => {
  return (
    <DefaultLayout padding={true}>
      <NsFormWrapper>{children}</NsFormWrapper>
    </DefaultLayout>
  );
};
