import { InteractionType, PublicClientApplication } from '@azure/msal-browser';
import { Box, TextareaAutosize, Typography, useTheme } from '@mui/material';
import { NsButton, NsFullPageSpinner, useNotifier } from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import TipologiaProvvedimentoEnum from '../enum/TipologiaProvvedimentoEnum';
import { AccettaModalProps } from '../interfaces';
import { useConfig } from '../shared/configuration.context';
import SceltaOscuramentoModal from './SceltaOscuramentoModal';
import { Msal } from 'src/utils/Msal';
import DialogModal from '../shared/DialogModal';

export default function AccettaModal({
  oscuramentoDeskCsp,
  firma,
  tipoProvvedimento,
  id,
  closeModal,
  closeDrawer,
  refreshData,
  refreshTable,
  elaborazioneDeposito,
  origine,
}: AccettaModalProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [messaggio, setMessaggio] = useState<string>();
  const {
    signAuthAzure,
    signClientId,
    signRedirect,
    signScope,
    enabledFirmaOtp,
  } = useConfig();

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: t('depositi.accettaModal.confermaPresaInCaricoFascicolo'),
    content: <></>,
    maxWidth: 'sm',
  });

  const closeModalOscuramento = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const handleTipoOscuramento = async () => {
    let content, title;
    content = (
      <SceltaOscuramentoModal
        firma={handleFirma}
        origine={origine || ''}
        oscuramentoDeskCsp={oscuramentoDeskCsp || false}
        elaborazioneDeposito={elaborazioneDeposito}
      />
    );
    title = `Selezionare il tipo oscuramento`;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const handleConferma = async () => {
    try {
      setIsLoading(true);
      const api = await APIWrapper;

      const body = {
        idDeposito: id,
        motivazione: messaggio,
      };
      
      await api.call(
        'POST',
        `accettazioneDepositi/accetta`,
        body
      );
  
      notify({ type: 'success', message: 'Accettato' });
      closeModal();
      if (closeDrawer) {
        closeDrawer();
      }
      if (refreshTable) {
        refreshTable();
      }
      if (refreshData) {
        router.push('/depositi');
        refreshData();
      }
    } catch (err: any) {
      console.log('error', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirma = async () => {
    if (origine === 'LOCALE' || oscuramentoDeskCsp) {
        console.log(elaborazioneDeposito)
      const api = await APIWrapper;
      await api.call(
        'POST',
        `elaborazionedeposito`,
        elaborazioneDeposito
      );
    }
    if (firma) {
      await closeModal();
      const isFirmaOtp =
        enabledFirmaOtp?.toString().toLowerCase() == 'true' ||
        enabledFirmaOtp?.toString().toLowerCase() == '1'
          ? true
          : false;
      if (isFirmaOtp) {
        firma('', isFirmaOtp);
      } else {
        firmaDepositaAzure(isFirmaOtp);
      }
    }
  };

  const accettaFirma = async (azureToken: string, isFirmaOtp: boolean) => {
    /*
        await APIWrapper.call(
          'POST',
          `accettazioneDepositi/accetta?idDeposito=${id}`
        );
        */
    if (firma) {
      firma(azureToken, isFirmaOtp);
    }
  };

  const firmaDepositaAzure = async (isFirmaOtp: boolean) => {
    try {
      const msalConfig = {
        auth: {
          clientId: signClientId,
          authority: signAuthAzure,
          knownAuthorities: [signAuthAzure],
          redirectUri: signRedirect,
        },
      };

      const msalInstance = await publicAzureClient(msalConfig);

      let loginRequest = {
        scopes: [signScope ? signScope : ''],
        interactionType: InteractionType.Popup,
      };
      await msalInstance.initialize();
      msalInstance
        .loginPopup(loginRequest)
        .then((response) => {
          if (response.accessToken) {
            Msal.sessionOn();
            accettaFirma(response.accessToken, isFirmaOtp);
          } else {
            notify({
              type: 'error',
              message: t('errors.token'),
            });
          }
        })
        .catch((error) => {
          console.error('Error getting token: ', error);
          notify({
            type: 'error',
            message: error,
          });
          return false;
        });
    } catch (e) {
      console.log('Error getting token: ', e);
      notify({
        message: 'Errore durante la firma',
        type: 'error',
      });
      return false;
    }
  };

  const publicAzureClient = async (msalConfig: any) => {
    return new PublicClientApplication(msalConfig);
  };

  /* In the function below I would like to render an ORDINANZA, SENTENZA or MINUTA SENTENZA, MINUTA ORDINANZA.
   * M.O.S. = Minute Ordinanze Sentenze
   * */
  const renderMOS = () => {
    if (
      tipoProvvedimento == TipologiaProvvedimentoEnum.ORDINANZA ||
      tipoProvvedimento == TipologiaProvvedimentoEnum.SENTENZA
    ) {
      if (oscuramentoDeskCsp) {
        return (
          <Typography variant="h4" mt={1} mb={2}>
            {t('depositi.accettaModal.confermaEProseguiConFirmaEPubblProvv')}
          </Typography>
        );
      }
      if (!oscuramentoDeskCsp) {
        return (
          <Typography variant="h4" mt={1} mb={2}>
            {t('depositi.accettaModal.confermandoFirmaEPubblicazioneProvv')}
          </Typography>
        );
      }
    }
    if (
      tipoProvvedimento == TipologiaProvvedimentoEnum.MINUTAORDINANZA ||
      tipoProvvedimento == TipologiaProvvedimentoEnum.MINUTASENTENZA
    ) {
      return (
        <Typography variant="h4" mt={1} mb={2}>
          {t('depositi.accettaModal.confermaAccettazioneDeposito')}
        </Typography>
      );
    }
    return <></>;
  };

  return (
    <>
      <Box>
        {renderMOS()}
        <Box display="flex" justifyContent="space-between">
          {tipoProvvedimento == TipologiaProvvedimentoEnum.ORDINANZA ||
          tipoProvvedimento == TipologiaProvvedimentoEnum.SENTENZA ? (
            <NsButton
              variant="contained"
              onClick={
                origine === 'LOCALE' || oscuramentoDeskCsp
                  ? handleTipoOscuramento
                  : handleFirma
              }
            >
              {t('buttonsLabel.conferma')}
            </NsButton>
          ) : (
            <Box style={{ width: '100%' }}>
              <form>
                <TextareaAutosize
                  style={{ width: '100%' }}
                  minRows={6}
                  name="messaggio"
                  onChange={(e) => setMessaggio(e.target.value)}
                />              
                <NsButton variant="contained" onClick={handleConferma}>
                  {t('buttonsLabel.conferma')}
                </NsButton>
              </form>
            </Box>
          )}
        </Box>
      </Box>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
      <DialogModal {...modalProps} />
    </>
  );
}
