import { Box, Grid, TextField, Typography } from '@mui/material';
import { NsButton } from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';

const labesTitle = { fontSize: '17px', fontWeight: 600 };
export default function DatiPrincipali() {
  const { t } = useTranslation();

  return (
    <Grid
      item
      p={2}
      sx={{
        background: '#FBFBFB',
        borderTop: '12px solid #E4E4E4',
        height: 'fit-content',
      }}
      xs={7}
    >
      <Box mt={1}>
        <Typography variant="h2">{t('fascicolo.datiPrincipali')}</Typography>
      </Box>
      <Box mt={2} sx={labesTitle}>
        {t('common.nrg')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          size="small"
          variant="filled"
          sx={{ width: '100%' }}
          value="9 febbraio 2023"
        />
      </Box>
      <Box mt={2} sx={labesTitle}>
        {t('common.parti')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          size="small"
          variant="filled"
          sx={{ width: '100%' }}
          value="Daniela Deri, Francesca Neri, Francesco .."
        />
      </Box>
      <Box mt={2} sx={labesTitle}>
        {t('common.reato')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          size="small"
          variant="filled"
          sx={{ width: '100%' }}
          value="Immigrazione"
        />
      </Box>
      <Box mt={2} sx={labesTitle}>
        {t('common.valorePonderale')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          size="small"
          variant="filled"
          sx={{ width: '100%' }}
          value="1"
        />
      </Box>
      <Box mt={2} sx={labesTitle}>
        {t('common.stato')}
      </Box>
      <Box sx={{ background: 'white' }}>
        <TextField
          size="small"
          variant="filled"
          sx={{ width: '100%' }}
          value="In bozza"
        />
      </Box>
      <NsButton
        sx={{ height: '35px', marginTop: '20px' }}
        variant="contained"
        color="primary"
      >
        {t('fascicolo.calendarioUdienza').toUpperCase()}
      </NsButton>
    </Grid>
  );
}
