import { Grid, Typography, useTheme } from '@mui/material';
import { formatDate } from '../shared/Utils';
import { useTranslation } from 'next-i18next';
import { NsButton } from '@netservice/astrea-react-ds';
import DetailRiuniti from '../fascicolo/DetailRiuniti';
import { useState } from 'react';
import DialogModal from '../shared/DialogModal';

export default function DatiDepositi({ deposito }: any) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const style = {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };
  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
  });

  const handleModal = async (param: string, data?: any) => {
    let content, title;
    if (param == 'allRiuniti' && data.idRicUdienza) {
      content = <DetailRiuniti idRicUdienza={data.idRicUdienza} />;
      title = t('fascicoli.fascicoliTable.visualizzazioneRiuniti');
      style.width = 600;
    } else {
      content = <></>;
      title = '';
    }
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };
  return (
    <>
      <Typography sx={{ width: '100%' }} variant="h2">
        {t('depositi.datiDeposito.datiDeposito')}
      </Typography>
      <DialogModal {...modalProps} />
      <Grid item xs={4}>
        <Grid mt={2}>
          <Typography variant="h3">{t('depositi.datiDeposito.nrg')}</Typography>
          <Typography variant="h4">
            {deposito?.nrg}
            {deposito?.ricercaRiunitiView?.principale ? (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() =>
                  handleModal('allRiuniti', deposito?.ricercaRiunitiView)
                }
              >
                {t('common.principale')}
              </NsButton>
            ) : (
              ''
            )}
          </Typography>
        </Grid>
        <Grid mt={2}>
          <Typography variant="h3">
            {t('depositi.datiDeposito.depositante')}:
          </Typography>
          <Typography variant="h4">{deposito?.depositante}</Typography>
        </Grid>
        <Grid mt={2}>
          <Typography variant="h3">
            {t('depositi.datiDeposito.dataDeposito')}
          </Typography>
          <Typography variant="h4">
            {formatDate(deposito?.dataDeposito, 'D MMMM YYYY')}
          </Typography>
        </Grid>
      </Grid>
      <Grid item xs={4}>
        <Grid mt={2}>
          <Typography variant="h3">{t('depositi.datiDeposito.id')}</Typography>
          <Typography variant="h4">{deposito?.idCat}</Typography>
        </Grid>
        <Grid mt={2}>
          <Typography variant="h3">
            {t('depositi.datiDeposito.presidenteCollegio')}:
          </Typography>
          <Typography variant="h4">{deposito?.presCollegio}</Typography>
        </Grid>
      </Grid>
      <Grid item xs={4}>
        <Grid mt={2}>
          <Typography variant="h3">
            {t('depositi.datiDeposito.oscuramentoDeskCsp')}
          </Typography>
          <Typography variant="h4">
            {deposito?.oscuramentoDeskCsp === 1 ? 'Si' : 'No'}
          </Typography>
        </Grid>
        <Grid mt={2}>
          <Typography variant="h3">{t('common.oscuramentoSic')}: </Typography>
          <Typography variant="h4">
            {deposito?.oscuramentoSic === 1 ? 'Si' : 'No'}
          </Typography>
        </Grid>
      </Grid>
    </>
  );
}
