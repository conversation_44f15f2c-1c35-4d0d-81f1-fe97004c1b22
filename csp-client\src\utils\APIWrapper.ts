import API from './API';
import axios from 'axios';
import { errorLogger, Severity } from './errorLogger';

const initializeAPI = async () => {
  let url = '';
  try {
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_PATH ?? ''}/api/configuration`,
      { timeout: 5000 } // Aggiungo un timeout di 5 secondi
    );
    url = data.urlServizi;
  } catch (e) {
    url = process.env.SERVIZI_CSP_URL ?? '';
    errorLogger.log({
      message: 'Error retrieving CSP service configuration',
      severity: Severity.ERROR,
      context: {
        error: e,
        defaultUrl: url
      },
      component: 'APIWrapper'
    });
    console.error(e);
    console.log(
      'Impossibile recuperare la configurazione del servizio CSP. URL impostato a default'
    );
  }

  try {
    return new API(
      url,
      'cspclient',
      'cspclient!',
      '/csp-backend/jsonapi/'
    );
  } catch (e) {
    errorLogger.log({
      message: 'Error initializing API',
      severity: Severity.ERROR,
      context: {
        error: e,
        url: url
      },
      component: 'APIWrapper'
    });
    throw e;
  }
};

const APIWrapper = initializeAPI();
export default APIWrapper;