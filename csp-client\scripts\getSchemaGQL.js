const parse = require('cross-fetch');
const buffer = require('buffer');
const fs = require('fs');

parse
  .fetch(
    'https://nexuspa.netserv.it/repository/binaries/products/SIGMA/sigma-gql-proxy-schema/schema.graphql'
  )
  .then((res) => {
    if (res.status >= 400) {
      throw new Error('Bad response from server');
    }
    return res.arrayBuffer();
  })
  .then((res) => {
    fs.writeFile('./schema.graphql', Buffer.from(res), (err) => {
      if (err) {
        console.error(err);
      }
    });
  })
  .catch((err) => {
    console.error(err);
  });
