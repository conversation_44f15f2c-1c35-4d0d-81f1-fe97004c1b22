import { Box, Grid, Modal, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { MainModalProps } from '../interfaces';
import { NsButton } from '@netservice/astrea-react-ds';

export default function MainModal({
  modal,
  style,
  closeModal,
  title,
  body,
}: Readonly<MainModalProps>) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  return (
    <Modal
      open={modal}
      onClose={closeModal}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style}>
        <Grid container>
          <Grid item xs={12}>
            <Grid container justifyContent="space-between" alignItems="center">
              <Grid item xs={10}>
                <Box
                  sx={{
                    fontSize: '20px',
                    fontWeight: 600,
                    marginTop: '10px',
                    marginLeft: '10px',
                  }}
                >
                  {title}
                </Box>
              </Grid>
              <Grid item xs={2} textAlign="right">
                <NsButton
                  sx={theme.custom.secondaryButton}
                  onClick={closeModal}
                >
                  {t('common.chiudi')}
                </NsButton>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            {body}
          </Grid>
        </Grid>
      </Box>
    </Modal>
  );
}
