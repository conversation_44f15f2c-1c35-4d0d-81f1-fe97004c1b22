{"server": {"errorCode": {"traduzioneAssente": "Traduzione assente del codice: {{code}}", "ERR_NETWORK": "Server non raggiungibile - Contattare il CED.", "GENERIC_ERROR": "Errore generico - Contattare il CED", "USER_PASS_INVALID": "Nome utente e/o password errati", "USER_UNAUTH_CSP": "Utente non abilitato alle funzioni del CSP", "USER_DEPOSITO_DIFF": "Operazione non consentita poiché l'utente loggato è diverso dall'utente abilitato alla firma", "CSP_COMMON_REST_WARNING": "Errore interno al server - Contattare il CED", "USER_NOT_FOUND": "Utente non presente in anagrafica - Contattare il CED", "TOKEN_NULL": "Errore in fase di autenticazione", "TOKEN_ERROR": "Errore durante il recupero delle informazioni", "FIRMA_REMOTA_GENERIC_ERROR": "Errore durante la firma", "FIELD_MANDATORY_ERROR": "Campi obbligatori mancanti", "ID_DEPOSITO_NULL": "Errore nel recupero del deposito - Contattare il CED", "IDCAT_DEPOSITO_NULL": "Errore nel recupero del deposito - Contattare il CED", "ID_PROVV_NULL": "Errore nel recupero del provvedimento - Contattare il CED", "NRG_NULL": "Errore nel recupero del provvedimento - Contattare il CED", "DEPOSITO_NOT_FOUND": "Deposito non trovato", "GL_GENERIC_ERROR": "Errore in gestore locale - Contattare il CED", "GL_DEPOSITO_NOT_FOUND": "Deposito nel gestore locale non trovato - Contattare il CED", "GL_REGISTRO_NOT_FOUND": "Registro nel gestore locale non trovato - Contattare il CED", "ELABORAZIONE_DEPOSITO_GENERIC_ERROR": "Errore elaborazione deposito - Contattare il CED", "ELABORAZIONE_DEPOSITO_SAVE_ERROR": "Errore durante la creazione elaborazione deposito", "ELABORAZIONE_DEPOSITO_DELETE_ERROR": "Errore durante l'eliminazione elaborazione deposito", "DEPOSITO_ACCETTATO_RIFIUTATO": "Impossibile completare l'operazione, il deposito risulta già accettato/rifiutato", "DEPOSITO_PRINCIPALE_NON_ACCETTATO": "Impossibile accettare, il deposito principale non è ancora stato accettato", "ACCETTAZIONE_DEPOSITI_GENERIC_ERROR": "Errore in fase di accettazione del deposito", "SESSION_EXPIRED": "Sessione scaduta, rieseg<PERSON>re il login", "ACCETTAZIONE_DEPOSITI_SAVE_ERROR": "Errore durante il salvataggio del provvedimento", "GESTIONE_SIC_GENERIC_ERROR": "Errore in fase di pubblicazione - Contattare il CED", "ACCETTAZIONE_DEPOSITO_SIC_ERROR": "Errore in fase di accettazione del deposito sul SIC - Contattare il CED", "RIFIUTA_DEPOSITO_GENERIC_ERROR": "Errore durante il rifiuto del deposito", "RIFIUTA_DEPOSITO_SAVE_ERROR": "Errore in fase di aggiornamento dei dati del ricorso", "DATI_RIFIUTI_NULL": "Dati rifiuto non indicati", "INIT_ISCRIZIONE_DEPOSITO_ERROR": "Errore in iscrizione deposito", "DEPOSITO_SEARCH_ERROR": "Errore durante la ricerca dei depositi - Contattare il CED", "EXPORT_DEPOSITI_ERROR": "Errore durante l'esportazione dei dati - Contattare il CED", "DEPOSITO_GENERIC_ERROR": "Errore in deposito provvedimento - Contattare il CED", "DEPOSITO_PUBBLICATO_SIC": "Il deposito è stato già pubblicato sul SIC", "DEPOSITO_DEPOSITATO_SIC": "Il deposito è stato già depositato sul SIC", "PRESA_IN_CARICO_GENERIC_ERROR": "Errore durante la presa in carico - Contattare il CED", "PRESA_IN_CARICO_DEPOSITO_ONLY_NEW": "Presa in carico possibile solo per i nuovi depositi", "PRESA_IN_CARICO_MULTIPLA_ERROR": "Errore durante la presa in carico massiva - Contattare il CED", "ID_DEPOSITI_NULL": "Lista depositi non valida", "RICORSO_SAVE_ERROR": "Errore in fase di salvataggio del ricorso", "DATA_PROVV_NULL": "Impossibile accettare il deposito, oscuramento dati/tipo verificato/dispositivo non valorizzati correttamente", "DATI_UDIEN_NULL": "Dati udienza mancanti", "PUBBLICAZIONE_GENERIC_ERROR": "Errore in fase di pubblicazione - Contattare il CED", "PUBBLICAZIONE_IN_CORSO": "Pubblicazione del provvedimento già in corso", "ATTO_NOT_FOUND": "Atto non trovato", "UPDATE_SENTENZA_ERROR": "Errore in fase di aggiornamento del provvedimento", "TIMBRI_SAVE_ERROR": "Errore durante il salvataggio dei timbri", "UCU_PUBB_ERROR": "Errore in fase di pubblicazione verso UCU - Contattare il CED", "UPDATE_DEPOSITO_ERROR": "Errore durante l'aggiornamento del deposito - Contattare il CED", "UPDATE_STATO_DESK_ERROR": "Errore durante l'aggiornamento dello stato sul DESK", "SEZIONI_NULL": "Sezione non presente", "UNAUTHORIZED_SIGN": "Firma non consentita poiché l'utente loggato è diverso dall'utente abilitato alla firma", "UNAUTHORIZED_PUBLISH": "Non si è abilitati al deposito e pubblicazione - Contattare il CED", "SEARCH_PUBBLICATO_NOT_ALLOWED": "Ricerca per stato PUBBLICATO non consentito per il tipo di provvedimento selezionato", "SEARCH_MINUTA_ACCETTATA_NOT_ALLOWED": "Ricerca per stato MINUTA ACCETTATA non consentito per il tipo di provvedimento selezionato", "DEPOSITO_NOT_NEW": "Modifica non consentita per il deposito selezionato", "STORICO_PROVV_ERROR": "Errore in fase di recupero delle informazioni del provvedimento", "EXPORT_UDIENZE_ERROR": "Errore durante l'export delle udienze", "MONITORAGGIO_GENERIC_ERROR": "Errore generico", "MONITORAGGIO_SEARCH_ERROR": "Errore durante la ricerca", "SEARCH_FASCICOLO_ERROR": "Errore durante la ricerca dei fascicoli", "RICORSI_GENERIC_ERROR": "Errore generico su ricorsi - Contattare il CED", "SEARCH_RICORSI_ERROR": "Errore durante la ricerca dei ricorsi", "REATI_RICORSO_ERROR": "Errore durante la ricerca dei reati del ricorso", "PARTI_RICORSO_ERROR": "Errore durante la ricerca delle parti del ricorso", "ANAGRAFICHE_GENERIC_ERROR": "Errore generico sulle anagrafiche - Contattare il CED", "FIND_MAGISTRATI_ERROR": "Errore durante la ricerca dei magistrati", "ATTI_GENERIC_ERROR": "Errore in fase di recupero degli atti - Contattare il CED", "ATTO_NOT_ACCEPTED": "L'atto non risulta ancora essere stato accettato", "DOWNLOAD_ATTO_ERROR": "Errore in fase di download degli atti", "AUTHENTICATION_GENERIC_ERROR": "Errore in fase di autenticazione - Contattare il CED", "ACCOUNT_NOT_VALID": "Account associato all'utente non è valido - Contattare il CED", "INIT_SESSION_ERROR": "Errore in fase di inizializzazione della sessione - Contattare il CED", "USER_READ_ONLY": "Utente abilitato in sola lettura", "RICORSO_NOT_FOUND": "Ricorso non trovato", "PROVV_NOT_FOUND": "Provvedimento non trovato", "DEPOSITO_NON_LAVORATO": "Il deposito non risulta ancora stato lavorato", "PROVV_NON_PUBB": "Impossibile accettare il deposito, provvedimento non pubblicabile", "RITRASMISSIONE_PROVV_ERROR": "Impossibile ritrasmettere la pubblicazione", "PUBBLICAZIONE_ERROR": "Accettato con errore di pubblicazione - Contattare il CED", "TIPO_PROVV_NOT_MATCHING": "Il provvedimento che si sta cercando di pubblicare è differente rispetto a quanto scaricato sul SIC", "PROVV_SEARCH_ERROR": "Errore durante la ricerca del provvedimento", "TIPO_MINUTA_INVALID": "Tipo minuta non valido, inserire esito udienza da SIC", "MINUTA_CONV_ERROR": "Errore in fase di conversione della data minuta", "USER_NOT_PRESENT_SIGN_ERROR": "Utente non presente - Contattare il CED", "INCORRECT_PASSWORD_SIGN_ERROR": "Password errata - Contattare il CED", "INCORRECT_PIN_SIGN_ERROR": "Pin errato - Contattare il CED", "USER_BLOCKED_SIGN_ERROR": "Utente bloccato - Contattare il CED", "DEPOSITO_GIA_ASSEGNATO": "Il deposito è già stato preso in carico", "INVALID_JWT_TOKEN_SIGN_ERROR": "Autenticazione ADN fallita. Riprovare dopo aver eseguito il logout e in caso di nuovo errore contattare il CED"}}, "common": {"dateFormat": "{{date, DD MMMM YYYY}}", "dateTemplate": "{{date, DD/MM/YYYY}}", "campiObbligatori": "I campi con * sono obbligatori", "datetimeFormat": "{{date, DD MMMM YYYY HH:mm}}", "datetimeFormatShort": "{{date, DD/MM/YYYY HH:mm}}", "compilareLeMotivazioni": "Compilare le motivazioni del rifiuto", "yes": "Sì", "no": "No", "submit": "Invia", "note": "Note", "reset": "Reset", "filtra": "Filtra", "error": {"title": "Si è verificato un errore imprevisto", "content": "È possibile scaricare un report dell'errore, o tornare alla pagina precedente", "downloadReport": "Scarica report", "goBack": "Torna indietro", "name": "Codice Errore: ", "retry": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "data": "Data", "info": "Info", "collegio": "Collegio", "sezione": "Sezione", "reato": "<PERSON><PERSON>", "stato": "Stato", "valorePonderale": "<PERSON><PERSON>", "parti": "Parti", "nrg": "NRG", "creaNuovo": "<PERSON><PERSON>", "tipologia": "Tipologia", "reati": "Reati", "autore": "Autore", "statoProvvedimento": "Stato Provvedimento", "ultimaModifica": "Ultima Modifica", "OscuramentoDeskCsp": "Oscuramento", "azioni": "Azioni", "gestioneDepositi": "Gestione Depositi", "ricercaRicorsi": "Ricerca Ricorsi", "chiudi": "<PERSON><PERSON>", "nomeCognome": "Nome e Cognome", "cognome": "Cognome", "ruolo": "<PERSON><PERSON><PERSON>", "vedi": "<PERSON><PERSON><PERSON>", "principale": "<PERSON><PERSON>", "vediTutte": "<PERSON>edi tutte", "selezionaOpzione": "Seleziona un'opzione", "applicaFiltri": "Applica filtri", "monitoraggio": "Monitoraggio", "accetta": "Accetta", "rifiuta": "<PERSON><PERSON><PERSON><PERSON>", "datiDeposito": "<PERSON><PERSON>", "esiti": "<PERSON><PERSON><PERSON>", "tipo": "Tipo", "depositante": "Depositante", "atto": "Atto", "presidenteCollegio": "Presidente Collegio", "oscuramentoDeskCsp": "Oscuramento DESK/CSP", "oscuramentoSic": "Oscuramento SIC", "presoDa": "Preso in carico da", "statoDeposito": "Stato Deposito", "accettato": "Accettato", "rifiutato": "Rifiutato", "pubblicato": "Pubblicato", "pubblicatoSic": "Pubblicato dal SIC", "confermandoRifiutaDeposito": "Confermando rifiuterai il deposito", "aggiorna": "Aggiorna", "esporta": "Esporta"}, "meta": {"title": "CSP Client Corte Suprema di Cassazione", "description": ""}, "header": {"openAccount": "Apri account", "account": "", "menu": "<PERSON><PERSON>", "labels": {"title": "CSP Client Corte Suprema di Cassazione"}, "links": {"dashboard": "Dashboard", "ricerca": "Ricerca", "anagrafica": "Anagrafica"}}, "fascicolo": {"calendarioUdienza": "Calendario Udienze", "fascicolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datiUdienza": "<PERSON><PERSON>", "fascicoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "termineDeposito": "<PERSON><PERSON><PERSON>", "datiPrincipali": "<PERSON><PERSON>", "provvedimenti": "Provvedimenti", "importa": "IMPORTA", "datiFascicolo": "<PERSON><PERSON>", "uploadAvviso": "Verificare la correttezza della tipologia di provvedimento caricato", "firmaDeposita": "Firma e Deposita", "annulla": "<PERSON><PERSON><PERSON>"}, "impostazioni": {"impostazioniUtenteCollegato": "Impostazioni utente collegato", "email": "Email", "nomeUtente": "Nome utente", "credenzialiFirmaRemota": "Credenziali firma remota", "password": "Password", "elimina": "Elimina", "conferma": "Conferma"}, "monitoraggio": {"tipo": "Tipo", "aula": "<PERSON><PERSON>", "collegio": "Collegio", "presidente": "Presidente", "relatore": "<PERSON><PERSON><PERSON>", "estensore": "Estensore", "parti": "Parti", "nrg": "NRG", "sezionale": "Sezionale", "tipoProvvedimento": "<PERSON><PERSON><PERSON>", "stato": "Stato", "totaleGiorni": "Totale Giorni", "udienza": "Udienza", "dataMinuta": "Data Minuta", "dataVisionePresidente": "Data messa in visione Presidente", "dataRichiestaUltimaModifica": "Data richiesta ultima modifica", "dataUltimaMinutaModificata": "Data deposito ultima minuta modificata", "dataDepositoProvvedimento": "Data deposito provvedimento", "dataPubblicazione": "Data pubblicazione", "azioni": "Azioni", "filtriAttivi": "<PERSON><PERSON><PERSON> attivi", "sezione": "Sezione", "dataUdienza": "Data Udienza", "monitoraggioUdienze": "Monitoraggio Udienze", "minutaSic": "Minuta depositata da SIC", "provvedimentoDepositatoSic": "Provvedimento depositato da SIC", "numeroRaccoltaGenerale": "Num. Racc. Gen", "pulisciCampi": "PULISCI CAMPI", "ricerca": "RICERCA", "form": {"datiUdienza": "<PERSON><PERSON>:", "sezione": "Sezione:", "numero": "Numero:", "dataUdienza": "Data Udienza:", "da": "Da:", "a": "A:", "tipoUdienza": "Tipo U<PERSON>nza:", "tipologia": "Tipologia:", "collegio": "Collegio:", "datiRicorso": "<PERSON><PERSON>:", "datiProvvedimento": "<PERSON><PERSON>:", "nrg": "NRG:", "estensore": "Estensore:", "cognomeParte": "Parti (cognome)", "tooltipParte": "Esclusi Procuratori e difensori", "presidenteCollegio": "Presidente Collegio:", "nomeECognome": "Nome e Cognome:", "dataPubblicazione": "Data Pubblicazione:", "selezionareData": "Selezionare una data", "nessunPresidenteDisponibile": "Nessun Presidente disponibile", "nessunEstensoreDisponibile": "<PERSON><PERSON><PERSON> disponibile", "nessunaParteTrovata": "Nessuna Parte trovata", "numeroRicorso": "Numero Ricorso:", "tipoRicorso": "Tipo Ricorso:", "presidente": "Presidente:", "dataMinuta": "Data Minuta:", "validazioneDataUdienza": "La data udienza Da non può essere successiva alla data di udienza A", "validazioneDataMinuta": "La data minuta Da non può essere successiva alla data minuta A", "validazioneDataPubblicazione": "La data pubblicazione Da non può essere successiva alla data pubblicazione A", "notaFiltriRicerca": "La sezione se non valorizzata di default da sistema deve essere valorizzata."}, "monitoraggioTable": {"monitora": "Monitora", "esporta": "Esporta", "dicituraAsterisco": "* Il totale giorni indica la differenza tra la data odierna e la\ndata dell' ultimo evento. Se il provvedimento è pubblicato il\ntotale giorni indica la differenza tra la data di pubblicazione e\nla data udienza", "visualizzazioneParti": "Visualizzazione parti", "monitoraggioNavetta": "Monitor<PERSON><PERSON>"}, "monitoraTable": {"vaiUltimaBustaDepositata": "Vai all'ultima busta depositata", "esporta": "Esporta", "dicituraAsterisco": "* Il totale giorni indica la differenza tra la data odierna e la\ndata dell' ultimo evento. Se il provvedimento è pubblicato il\ntotale giorni indica la differenza tra la data di pubblicazione e\nla data udienza", "dicituraAsteriscoRosso": "Gli eventi verificatesi tramite il SIC sono visibili nel monitoraggio della navetta attraverso gli stati: minuta depositata da SIC, provvedimento depositato da SIC, pubblicato da SIC"}}, "calendario": {"ordine": "<PERSON><PERSON>", "nrg": "NRG", "parti": "Parti", "valore": "<PERSON><PERSON>", "stato": "Stato", "oscuramentoSic": "Oscuramento SIC", "oscuramentoDeskCsp": "Oscuramento DESK/CSP", "provvedimenti": "Provvedimenti", "azioni": "Azioni", "reato": "Reati", "semplificata": "Semplif.", "valorePonderale": "Val. Pon.", "tipologia": "Tipologia Provv.", "dataUdienza": "Data Udienza", "dataDeposito": "Data Deposito"}, "intestazioni": {"nrg": "NRG", "parti": "Parti", "valore": "Valore", "stato": "Stato Provvedimento", "reato": "<PERSON><PERSON>", "oscuramento": "Oscuramento", "valorePonderale": "<PERSON><PERSON>", "tipologia": "Tipologia", "scaricaintestazioni": "Scarica Intestazioni"}, "buttonsLabel": {"firma": "FIRMA", "conferma": "CONFERMA", "annulla": "ANNULLA", "caricaDaFile": "CARICA DA LOCALE", "confermaInoltra": "CONFERMA E INOLTRA AL PRESIDENTE"}, "dragDrop": {"labels": {"textDrag": "Trascina i documenti all'interno dell'area tratteggiata o carica i file da locale tramite l'apposito bottone", "rilasciaFile": "Rilascia il file"}}, "errors": {"tipoAllegatoRequired": "Specificare la tipologia dell'allegato", "token": "Errore durante l'acquisizione del token"}, "parti": {"parte": "Parte", "avvocati": "Avvocati"}, "depositi": {"accettaModal": {"confermaPresaInCaricoFascicolo": "Conferma presa in carico della busta di deposito per il fascicolo", "confermaEProseguiConFirmaEPubblProvv": "Confermando si proseguirà alla firma e alla pubblicazione del provvedimento e sarà apposto il timbro di oscuramento", "confermaAccettazioneDeposito": "Confermando si invia al Presidente la minuta con il seguente messaggio", "confermandoFirmaEPubblicazioneProvv": "Confermando si proseguirà alla firma e alla pubblicazione del provvedimento"}, "apriCaricamento": {"depositoPresoInCarico": "Deposito preso in carico correttamente", "attualmenteInCaricoAdAltroUtente_1": "Attualmente il deposito del fascicolo", "attualmenteInCaricoAdAltroUtente_2": "è in carico ad un altro utente. Confermi di volerlo prendere in carico?", "confermandoPresaInCaricoDeposito": "Confermando le verrà associata la presa in carico del deposito"}, "datiDeposito": {"datiDeposito": "<PERSON><PERSON>o", "nrg": "NRG:", "depositante": "Depositante", "dataDeposito": "Data deposito:", "id": "ID:", "presidenteCollegio": "Presidente Collegio", "oscuramentoDeskCsp": "Oscuramento DESK/CSP:", "dePlano": "De plano:"}, "depositoFilter": {"sezione": "Sezione", "dataUdienza": "Data Udienza", "tipoUdienza": "Tipo <PERSON>", "collegio": "Collegio", "numeroRicorso": "Numero Ricorso", "annoRicorso": "<PERSON><PERSON>", "depositante": "Depositante", "stato": "Stato", "atto": "Atto", "dataDeposito": "Data Deposito", "oscuramentoDeskCsp": "Oscuramento DESK/CSP", "diagnosi": "<PERSON><PERSON><PERSON><PERSON>", "daAccettare": "Da accettare", "minutaAccettata": "Minuta accettata", "rifiutato": "Rifiutato", "pubblicato": "Pubblicato", "inErroreDiPubblicazione": "In errore di pubblicazione", "tipoProvvedimento": "<PERSON><PERSON><PERSON>"}, "detailDeposito": {"dataEOra": "Data e ora", "evento": "Evento", "descrizione": "Stato Provvedimento", "note": "Note", "nRaccGen": "N. Racc. Gen. :", "gestioneDepositi": "Gestione depositi", "dettaglioDepositoFascicolo": "Dettaglio deposito fascicolo", "prendiCarico": "Prendi in carico"}, "_diagnosi": {"diagnosi": "<PERSON><PERSON><PERSON><PERSON>", "storiaProvvedimento": "Storia provvedimento"}, "_fileBusta": {"fileBusta": "File busta", "fileBustaNotifica": "Il provvedimento è stato redatto con la modalità importa file. Qualora il magistrato abbia prescritto nel testo del provvedimento la necessità di procedere con l’oscuramento occorre selezionare il relativo timbro nel flusso di pubblicazione telematica."}, "firmaModal": {"username": "Username", "password": "Password", "annulla": "<PERSON><PERSON><PERSON>", "firmaEDeposita": "Firma e deposita", "firmaErrore": "Errore durante la firma", "firmaNonAbilitata": "Firma non abilitata", "warningUtenteNonInAnagrafica": "Utente non presente in anagrafica - Contattare il CED", "warningSicPerUtenteNonAbilitatoAllaPubblicazione": "Non si è abilitati al deposito e pubblicazione - Contattare il CED", "firmaNonConsentita": "Firma non consentita poiché l'utente loggato è diverso dall'utente abilitato alla firma"}, "ordinanza": {"minutaDiSentenza": "Minuta di Sentenza", "minutaDiOrdinanza": "Minuta di Ordinanza", "sentenza": "Sentenza", "ordinanza": "Ordinanza", "nonOscurata": "Non oscurata", "oscurata": "Oscurata", "oscuramentoDeskCsp": "Oscuramento DESK/CSP", "nonOscurato": "non oscurato"}, "sceltaOscuramentoModal": {"inCasoDiDiffusioneProvv": "In caso di diffusione del presente provvedimento omettere le generalità e gli altri dati identificativi, a norma dell'art. 52 d.lgs. 196/2003 e ss.mm", "dispostoUfficio": "Disposto d'ufficio", "aRichiestaDiParte": "A richiesta di parte", "impostoDallaLegge": "Imposto dalla legge", "nessunTimbro": "<PERSON><PERSON><PERSON> timbro", "si": "Sì", "no": "No"}, "filtriElencoDepositi": "Filtri elenco depositi", "filtriImpostati": "Fi<PERSON>ri impostati", "aggiorna": "Aggiorna", "gestioneDepositi": "Gestione depositi", "elencoDepositi": "Elenco depositi", "depositoFascicolo": "Deposi<PERSON> fasci<PERSON>", "apriDettaglio": "<PERSON><PERSON>", "prendiCarico": "Prendi in carico", "diagnosi": "<PERSON><PERSON><PERSON><PERSON>", "fileBusta": "File busta", "scaricaTutto": "<PERSON><PERSON><PERSON> tutto", "attoPrincipale": "ATTO PRINCIPALE", "nonOscurata": "Non oscurata", "oscurata": "Oscurata", "oscuramentoDeskCsp": "Oscuramento DESK/CSP", "dataDeposito": "Data Deposito", "dataLavorazione": "Data Lavorazione", "dataUdienza": "Data Udienza", "numeroRicorso": "Numero Ricorso", "annoRicorso": "<PERSON><PERSON>", "tipoUdienza": "Tipo <PERSON>", "collegio": "Collegio", "sezione": "Sezione", "cognomeParte": "Parti (cognome)", "atto": "Atto", "depositante": "Depositante", "stato": "Stato", "username": "Username", "password": "Password", "otp": "Otp", "testoCheckbox": "Da accettare", "numRaccGen": "N. Racc. Gen.", "minute": "Minute", "sentenze/ordinanze": "Sentenze/Ordinanze", "esportaDepositi": "Esporta depositi", "dataDa": "Data deposito da", "dataA": "Data deposito a", "erroreDate": "L'ampiezza del periodo selezionato non deve superare 30 giorni", "erroreSezione": "Campo \"Sezione\" obbligatorio", "erroreProvvedimentoLavoratoDaSIC": "Il provvedimento è già lavorato da SIC", "fileScaricato": "File scaricato correttamente", "scaricamentoFallito": "Scaricamento fallito", "daPrendereInCarico": "Da prendere in carico", "azioniMassive": "Azioni massive", "presaInCaricoMassiva": "Conferma presa in carico massivo", "presaInCaricoMassivaDisabled": "Si può accedere alla presa in carico massiva solo se è attiva una delle due combinazioni di filtro seguenti e se l'utente ha poi selezionato alcuni o tutti i depositi risultanti dai filtri: 'Da prendere in carico + minute' oppure 'Da prendere in carico + sentenze/ordinanze'", "depositiPresiInCarico": "Depositi presi in carico correttamente", "depositiSelezionati": "{{count}} depositi selezionati", "confirmPrendiInCarico": "Confermando le verrà associata la presa in carico di tutti i provvedimenti selezionati", "prendiInCarico": "Prendi in carico", "sentenzeOrdinanze": "Sentenze/Ordinanze", "daAccettare": "Da accettare"}, "fascicoli": {"detailReati": {"reato": "<PERSON><PERSON>"}, "fascicoliFilter": {"dataUdienzaONumeroEAnnoRicorsoOParte": "Inserire la data udienza oppure numero e anno del ricorso o il cognome di una delle parti", "nonEsistonoRicorsiPerUdienza": "Non esistono ricorsi associati all'udienza", "ricorsoCercatoNonHaUdienza": "Il ricorso cercato non è associato all'udienza indicata"}, "fascicoliTable": {"visualizzazioneReati": "Visualizzazione reati", "visualizzazioneParti": "Visualizzazione parti", "visualizzazioneProvvPubblicato": "Visualizza provvedimento pubblicato", "downloadProvvPubblicatoOscurato": "Download provvedimento pubblicato oscurato", "visualizzazioneRiuniti": "Fascicoli riuniti"}, "ricercaRicorso": "Ricerca ricorso", "elencoFascicoli": "<PERSON><PERSON><PERSON> fasci<PERSON>li", "nessunRisultato": "Non vi è alcun risultato per i parametri di ricerca inseriti", "pulisciCampi": "<PERSON><PERSON><PERSON><PERSON> campi", "ricercaRicorsi": "Ricerca ricorsi", "ricerca": "Ricerca", "campiObbligatori": "È necessario inserire la data udienza, oppure numero e anno del ricorso o il cognome di una delle parti. Dalla ricerca per parti sono esclusi i Procuratori e i difensori.", "ruolo": "<PERSON><PERSON><PERSON>", "esitoUdienza": "Esito udienza"}, "menuHeader": {"menu": {"homePage": "Home Page", "calendarioUdienze": "Calendario Udienze"}}, "shared": {"datiUdienza": {"termineDeposito": "<PERSON><PERSON><PERSON>"}, "sideModal": {"confermaPresaInCaricoFascicolo": "Conferma presa in carico fascicolo", "dataOra": "Data e ora", "evento": "Codice evento", "tipologiaEvento": "Tipologia evento", "descrizione": "Descrizione", "firmaDelDocumento": "Firma del documento", "stato": "Stato:", "utente": "Utente:", "data": "Data:", "nRaccGen": "N. Racc. Gen. :", "apriDettaglio": "<PERSON><PERSON>", "prendiCarico": "Prendi in carico", "pubblicazioneDisabled": "Le funzionalità di pubblicazione del provvedimento sono disabilitate perchè l'utente non è autorizzato. Per ottenere l'autorizzazione rivolgersi al CED"}}, "form": {"buttons": {"submit": "Invia", "reset": "Can<PERSON><PERSON>", "annulla": "<PERSON><PERSON><PERSON>", "conferma": "Conferma", "mostra": "Mostra", "nascondi": "Nascondi"}, "errors": {"required": "Campo obbligatorio", "compilareLeMotivazioni": "Compilare le motivazioni del rifiuto", "invioUCUError": "Accettato. Errore durante invio verso ufficio copie"}, "warnings": {"aggiornareSIC": "Aggiornare su SIC Il dato di oscuramento ove non coerente"}, "filters": {"sezione": "Sezione", "dataUdienzaDa": "Data Udienza da", "dataUdienzaA": "Data Udienza a", "tipoUdienza": "Tipo <PERSON>", "collegio": "Collegio", "estensore": "Estensore", "cognomeParte": "Parti (cognome)", "relatore": "<PERSON><PERSON><PERSON>", "presidente": "Presidente Collegio", "dataMinutaDa": "<PERSON>", "dataMinutaA": "Data Minuta a", "dataPubblicazioneDa": "Data Pubblicazione da", "dataPubblicazioneA": "Data Pubblicazione a", "nrgDa": "Numero nrg da", "nrgDa2": "<PERSON>o nrg da", "nrgA": "Numero nrg a", "nrgA2": "Anno nrg a"}}, "spinner": {"loading": "Caricamento in corso..."}, "login": {"accedi": "Accedi", "dimenticatoPassword": "Dimenticato password", "password": "Password", "nomeUtente": "Nome utente", "login": "<PERSON><PERSON>"}, "pages": {"404": {"paginaORisorsaNonTrovata": "Pagina o risorsa non trovata", "indirizzoWebCorretto": "Quando si digita l'indirizzo web, verificare che sia corretto.", "seIndirizzoIncollatoControllalo": "Se avete incollato l'indirizzo web, controllate che l'intero\nindirizzo sia corretto.", "erroreAncoraPresenteContattaHelpDesk": "Se l'indirizzo web è corretto ma l'errore è ancora visibile, \n contattare l'help desk.", "tornaAllaPaginaPrecedente": "Torna alla pagina precedente"}, "500": {"erroreInternoAlServer": "Errore interno al server", "indirizzoWebCorretto": "Quando si digita l'indirizzo web, verificare che sia corretto.", "seIndirizzoIncollatoControllalo": "Se avete incollato l'indirizzo web, controllate che l'intero\nindirizzo sia corretto.", "erroreAncoraPresenteContattaHelpDesk": "Se l'indirizzo web è corretto ma l'errore è ancora visibile, \n contattare l'help desk.", "tornaAllaPaginaPrecedente": "Torna alla pagina precedente"}, "custom503": {"servizioNonDisponibile": "Servizio non disponibile", "messageOf503": "Il server non è temporaneamente in grado di soddisfare la richiesta a causa di un'interruzione della manutenzione o di problemi di capacità. Si prega di riprovare più tardi.", "contattareCED": "Contattare il CED"}}, "info": {"versioni": "Versioni", "noErrors": "<PERSON><PERSON><PERSON> errore trovato", "downloadErrors": "Scarica log console web", "errorsDownloaded": "Log console web scaricata correttamente", "errorDownloadingErrors": "Errore nello scaricamento del log console web"}}