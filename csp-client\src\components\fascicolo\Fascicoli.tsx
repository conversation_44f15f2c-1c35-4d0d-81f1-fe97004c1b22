import { Grid, Typography, useTheme } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { formatDate, getStyledModal, stateMap, tipoProvvedimentoMap } from '../shared/Utils';
import FascicoliFilter from './FascicoliFilter';
import FascicoliTable from './FascicoliTable';
import APIWrapper from 'src/utils/APIWrapper';
import { InputPayloadBackend, PageInfoProps } from '../interfaces';

export default function Fascicoli() {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const style = getStyledModal(theme);
  const [result, setResult] = useState<any>();
  const [message, setMessage] = useState<string>('');
  const [pageInfoRequest, setPageInfoRequest] = useState<PageInfoProps>({
      pageNumber: 0,
      pageSize: 25,
      order: {
          propertyName: 'nrg',
          asc: true,
        }
  })
  const [pageInfoServer, setPageInfoServer] = useState<PageInfoProps>({})
  const [criteriaParameters, setCriteriaParameters] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleApplyFilters = (
    newCriteriaParametes: any,
    validationMessage: string,
    clearData: boolean
  ) => {
    const pageInfo = {...pageInfoRequest, pageNumber: 0}; 
    setMessage(validationMessage);
    setPageInfoRequest(pageInfo);
    setCriteriaParameters({...criteriaParameters, ...newCriteriaParametes})
    if (clearData) {
      setResult([]);
      return;
    }
    const requestBody: InputPayloadBackend = {
      pageInfo: pageInfo, 
      criteriaParameters: newCriteriaParametes
    };
    getData(requestBody);    
  }

  const handleApplyPagination = (
    newPageInfo: PageInfoProps,
  ) => {
    const pageInformation: PageInfoProps = {...pageInfoRequest, ...newPageInfo}   
    setPageInfoRequest(pageInformation);
    const requestBody: InputPayloadBackend = {
      pageInfo: pageInformation, 
      criteriaParameters: criteriaParameters
    };
    getData(requestBody); 
  }

  const handleApplySorting = (property: string) => {
      const isAsc = !(pageInfoRequest.order?.propertyName === property && pageInfoRequest.order?.asc);
      const newOrder: PageInfoProps = {
        order: 
          {
            propertyName: property,
            asc: isAsc,
          },  
      };  
      const pageInfo = {...pageInfoRequest, ...newOrder};
      handleApplyPagination(pageInfo);
    };

  const mapRicorsiData = (ricorsi: any) => {
    return ricorsi?.map((item: any) => {
     return {
        id: item?.ricercaRicorsiBaseInUdienzaPK?.nrg,
        idUdienza: item?.ricercaRicorsiBaseInUdienzaPK?.idUdienza,
        dataudienza: item.dataUd ? formatDate(item.dataUd, 'DD/MM/YYYY') : '',
        sezioneUdienza: item.sezioneUd,
        tipo: item.tipoUd,
        numRaccGenerale: item.numRaccGenerale,
        collegio: item?.aula,
        nrg: `${item.numero}/${item.anno}`,
        numOrd: item.numOrd,
        valPond: item.valPond,
        tipoProvvedimento: tipoProvvedimentoMap[item.tipoProvvedimento],
        statoProvvedimento: item.statoProvvedimento,
        stato: stateMap[item.statoProvvedimento],
        oscuramentoSic: item.oscuramentoSic,
        oscuramentoDeskCsp: item.oscuramentoDeskCsp,
        reato: item.reato,
        detParti: item.detParti,
        attoOscurato: item.attoOscurato,
        attoNonOscurato: item.attoNonOscurato,
        annoPadre: item?.ricercaRiunitiView?.annoPadre,
        numeroPadre: item?.ricercaRiunitiView?.numeroPadre,
        riunito: item?.ricercaRiunitiView?.riunito,
        isPrincipale: item?.ricercaRiunitiView?.principale,
        ricercaRiunitiView: item.ricercaRiunitiView,
        origine: item.origine,
        esitoUdienza: item.esitoUdienza,
        ruolo: item.ruolo,
      };
    }) ?? [];
  }

  const getData = async (
    requestBody: InputPayloadBackend,
  ) => {
    try {
      setIsLoading(true);
      const api = await APIWrapper;
      const { response, message } = await api.call(
        'POST',
        'ricorsi',
        requestBody
      );
      const mapped = mapRicorsiData(response?.content);
      setPageInfoServer(response?.pageInfo);
      setResult([...mapped]);
      setMessage(message);
    } catch (err: any) {
      console.log('error', err);
    } finally {
      setIsLoading(false);
    }
  };  

  return (
    <Grid container>
      <Typography mt={2} mb={4} variant="h1" style={{ fontSize: '3rem' }}>
        {t('fascicoli.ricercaRicorsi')}
      </Typography>
      <FascicoliFilter
        actionApplyFilter={handleApplyFilters}
        isLoading={isLoading}
      />
      {result?.length > 0 && (
        <Grid container mt={5} p={2} border={theme.custom.borders[0]}>
          <Grid item xs={12} display="flex" justifyContent="space-between">
            <Typography variant="h1">
              {t('fascicoli.elencoFascicoli')}
            </Typography>
          </Grid>
          <Grid item mt={2} xs={12}>
            <FascicoliTable result={result} pageInfoServer={pageInfoServer} actionApplyPagination={handleApplyPagination} actionApplySorting={handleApplySorting}/>
          </Grid>
        </Grid>
      )}
      {result?.length === 0 && !message && (
        <Typography mt={5}>{t('fascicoli.nessunRisultato')}</Typography>
      )}
      {result?.length === 0 && message && (
        <Typography mt={5}>{message}</Typography>
      )}
    </Grid>
  );
}
