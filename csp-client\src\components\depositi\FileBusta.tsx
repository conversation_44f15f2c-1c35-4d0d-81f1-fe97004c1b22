import DownloadIcon from '@mui/icons-material/Download';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import { Box, Grid, Typography, useTheme } from '@mui/material';
import {NsButton, NsFullPageSpinner, NsTooltip, useNotifier} from '@netservice/astrea-react-ds';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { Esito, FileBustaProps } from '../interfaces';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import DialogModal from '../shared/DialogModal';

const style = {
  optoins: {
    width: '35px',
    height: '35px',
    background: '#E0EEEC',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#4F757A',
    cursor: 'pointer',
  },
  atto: {
    padding: '7px',
    background: '#D7E9E6',
    borderRadius: '20px',
    color: '#6B9380',
    fontSize: '14px !important',
    fontWeight: 600,
  },
};

const PDFViewer = dynamic(() => import('./PdfViewer'), {
  ssr: false,
});

export default function FileBusta({
  esiti,
  origine,
  idCatBusta,
}: FileBustaProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const { notify } = useNotifier();
  const [isLoading, setIsLoading] = useState(true);

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  useEffect(() => {
    setIsLoading(false);
  }, []);

  const Mstyle = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '80%',
    maxHeight: '800px',
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    overflowX: 'auto',
    p: 2,
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
  });

  const downloadAtto = async (id: string) => {
    try {
      setIsLoading(true);
      const api = await APIWrapper;
      await api.download(`atti/download/?idCat=${id}`);
      setIsLoading(false);
      notify({
        message: t('depositi.fileScaricato'),
        type: 'success',
      });
    } catch (err: any) {
      setIsLoading(false);
      notify({
        message: t('depositi.scaricamentoFallito'),
        type: 'error',
      });
    }
  };

  const downloadAll = async () => {
    try {
      setIsLoading(true);
      const requestPayload = {
        idCatBusta: idCatBusta,
        daScaricare: esiti.map((esito) => esito.catId),
      };
      const api = await APIWrapper;
      await api.download(`atti/downloadFilesBusta`, requestPayload);
      setIsLoading(false);
      notify({
        message: t('depositi.fileScaricato'),
        type: 'success',
      });
    } catch (err: any) {
      setIsLoading(false);
      notify({
        message: t('depositi.scaricamentoFallito'),
        type: 'error',
      });
    }
  };

  const downloadPdf = async (id: string) => {
    try {
      const api = await APIWrapper;
      setIsLoading(true);
      const blob = await api.downloadByteArray(
        `atti/download/?idCat=${id}`
      );
      setIsLoading(false);
      window.open(URL.createObjectURL(blob));
    } catch (err: any) {
      setIsLoading(false);
      notify({
        message: t('depositi.scaricamentoFallito'),
        type: 'error',
      });
    }
  };

  return isLoading ? (
    <NsFullPageSpinner isOpen={isLoading} value={100} />
  ) : (
    <>
      <Grid xs={12} item alignItems="center">
        <Typography pt={2} mb={2} variant="h2">
          {t('depositi._fileBusta.fileBusta')}
        </Typography>
        {origine === 'LOCALE' && (
          <Box display="flex">
            <WarningAmberIcon
              sx={{ color: 'red', marginRight: '5px', fontSize: '30px' }}
            />
            <Typography variant="h5" color="red">
              {t('depositi._fileBusta.fileBustaNotifica')}
            </Typography>
          </Box>
        )}

        {
          esiti.length > 0 && (
          <NsButton
            sx={theme.custom.borderButton}
            onClick={downloadAll}
            size="small"
            variant="contained"
          >
            {t('depositi.scaricaTutto')}
          </NsButton>
        )}
      </Grid>
      {esiti?.map((esito: Esito, i: number) => {
        return (
          <Grid
            xs={12}
            mt={3}
            key={i}
            display="flex"
            justifyContent="space-between"
            border={theme.custom.borders[0]}
            p={2}
            item
            className="blockMobile"
          >
            <Box display="flex" alignItems="center">
              <InsertDriveFileIcon color="primary" />
              <Typography ml={2} mr={2} variant="h3">
                {esito.nome}
              </Typography>
              {esito.status && (
                <Typography
                  sx={{
                    ...style.atto,
                    display: 'inline-block',
                    whiteSpace: 'nowrap',
                    overflow: 'visible',
                  }}
                >
                  {esito.tipo}
                </Typography>
              )}
            </Box>

            <Box display="flex">
              {esito.nome.includes('.pdf') && (
                <Box mr={1} sx={style.optoins}>
                  <NsTooltip
                    title="Open"
                    icon={
                      <RemoveRedEyeIcon
                        fontSize="small"
                        onClick={() => downloadPdf(esito.catId)}
                      />
                    }
                  />
                </Box>
              )}
              <Box sx={style.optoins}>
                <NsTooltip
                  title="Download"
                  icon={
                    <DownloadIcon
                      fontSize="small"
                      onClick={() => downloadAtto(esito.catId)}
                    />
                  }
                />
              </Box>
            </Box>
          </Grid>
        );
      })}
      <DialogModal {...modalProps} />
    </>
  );
}
