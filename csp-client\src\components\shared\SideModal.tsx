import InfoIcon from '@mui/icons-material/Info';
import { Box, Drawer, Grid, Typography, useTheme } from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
} from '@netservice/astrea-react-ds';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getUserData, pubblicazioneDisabled } from 'src/utils/Authentication';
import AccettaModal from '../depositi/AccettaModal';
import ApriCaricamento from '../depositi/ApriCaricamento';
import Diagnosi from '../depositi/Diagnosi';
import FileBusta from '../depositi/FileBusta';
import FirmaModal from '../depositi/FirmaModal';
import RifiutaModal from '../depositi/RifiutaModal';
import StatoProvvedimentoDeskEnum from '../enum/StatoProvvedimentoDeskEnum';
import StatoProvvedimentoEnum from '../enum/StatoProvvedimentoEnum';
import TipologiaProvvedimentoEnum from '../enum/TipologiaProvvedimentoEnum';
import { RightSideModalProps, SidemodalData } from '../interfaces'; 
import { formatDate, mapEsitiAndDiagnosi, tipoMap } from './Utils';
import DialogModal from './DialogModal';

export default function SideModal({
  openDrawer,
  closeDrawer,
  refreshTable,
  data,
}: RightSideModalProps) {
  const theme: any = useTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const [openTooltip, setOpenTooltip] = useState<boolean>(false);

  const tooltipClose = () => {
    setOpenTooltip(false);
  };

  const tooltipOpen = () => {
    setOpenTooltip(true);
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 500,
    bgcolor: 'background.paper',
    border: theme.custom.borders[0],
    boxShadow: 24,
    p: 2,
  };

  const closeModal = (value: any) => {
    //se value è false significa che si sta cliccando fuori la modale e non deve essere chiusa
    if (value !== false) {
      setModalProps({ ...modalProps, isOpen: false });
    }      
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    style,
    title: t('shared.sideModal.confermaPresaInCaricoFascicolo'),
    content: <></>,
    maxWidth: 'sm',
  });

  const firma = (azureToken?: string, firmaOtp?: boolean) => {
    let content, title;
    content = (
      <FirmaModal
        closeModal={closeModal}
        refreshData={refreshTable}
        id={data.id}
        azureToken={azureToken}
        isFirmaOtp={firmaOtp}
        closeDrawer={closeDrawer}
      />
    );
    title = t('shared.sideModal.firmaDelDocumento');
    style.width = 400;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const openDetail = (data: SidemodalData) => {
    router.push(`/depositi/${data.id}`);
  };

  const handleModal = (data: SidemodalData) => {
    const styledWidth = 550;

    setModalProps({
      ...modalProps,
      content: (
        <ApriCaricamento
        data={data} // Pass the whole aggregated data object
        closeModal={closeModal}
        idUtente={data.deposito?.idUtente} // Access deposito from aggregated data
        closeDrawer={closeDrawer}
      />
      ),
      title: `Conferma presa in carico - fascicolo nrg ${data.nrg}`,
      isOpen: true,
      style: { ...style, width: styledWidth },
    });
  };

  const acceptDeposito = async (data: SidemodalData) => {
    let content, title;
      content = (
        <AccettaModal
          oscuramentoDeskCsp={data.deposito?.oscuramentoDeskCsp} // Access from data prop
          firma={firma}
          tipoProvvedimento={data.deposito?.tipo} // Access from data prop
          closeModal={closeModal}
          id={data.id}
          closeDrawer={closeDrawer}
          elaborazioneDeposito={data.elaborazioneDeposito} // Access from data prop
          refreshTable={refreshTable}
          origine={data.deposito?.origine} // Access from data prop
        />
    );
    title = `Accettazione deposito fascicolo ${data.nrg}`;
    style.width = 550;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const rifiutaDeposito = async (data: SidemodalData) => {
    let content, title;
    content = (
      <RifiutaModal
        closeModal={closeModal}
        id={data.id}
        closeDrawer={closeDrawer}
        refreshTable={refreshTable}
      />
    );
    title = `Rifiuto deposito fascicolo ${data.nrg}`;
    style.width = 550;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const { diagnosi: diagnosiRows } = mapEsitiAndDiagnosi(data.sideBarDetails || []);
  const esitiForFileBusta = (data.contenuti || []).map((esito: any) => {
    const tipo = esito.tipo || '';
    return { ...esito, tipo: tipoMap[tipo] };
  });
  const idNrgFromElab = data.elaborazioneDeposito?.deposito?.nrg; 

  return (
    <>
      <Drawer anchor="right" open={openDrawer} onClose={closeDrawer}>
        <Box p={5} sx={{ width: 800 }}>
          <Grid container alignItems="center" justifyContent="space-between">
            <Typography variant="h1">
              {t('depositi.depositoFascicolo')} NRG. {data.nrg} {data.data}
              {data.dePlano == 1 && (
                <Box component="span" sx={{ marginLeft: '8px', background: '#E0E0E0', color: '#666666', padding: '0 10px', borderRadius: '5px', fontSize: '15px' }}>
                  DE PLANO
                </Box>
              )}
            </Typography>
            <NsButton sx={theme.custom.secondaryButton} onClick={closeDrawer}>
              {t('common.chiudi')}
            </NsButton>
          </Grid>
          <Grid item container xs={12}>
            {(data.deposito?.tipo == TipologiaProvvedimentoEnum.MINUTASENTENZA ||
              data.deposito?.tipo == TipologiaProvvedimentoEnum.MINUTAORDINANZA) &&
              data.deposito?.stato == StatoProvvedimentoEnum.ACCETTATO && (
                <Grid item xs={12}>
                  <Typography variant="h3">
                    {t('shared.sideModal.stato')} {data.deposito?.stato} -{' '}
                    {t('shared.sideModal.utente')} {data.deposito?.utenteInCarico}{' '}
                    - {t('shared.sideModal.data')}{' '}
                    {formatDate(data.deposito.dataAccettazione, 'DD/MM/YYYY')}
                  </Typography>
                </Grid>
              )}
            {(data.deposito?.tipo == TipologiaProvvedimentoEnum.SENTENZA ||
              data.deposito?.tipo == TipologiaProvvedimentoEnum.ORDINANZA) &&
              data.deposito?.stato == StatoProvvedimentoEnum.ACCETTATO && (
                <Grid item xs={12}>
                  <Typography variant="h3">
                    {t('shared.sideModal.nRaccGen')}{' '}
                    {data.deposito.numRaccGenerale}
                  </Typography>
                </Grid>
              )}
            {(data.deposito?.tipo == TipologiaProvvedimentoEnum.MINUTASENTENZA ||
              data.deposito?.tipo == TipologiaProvvedimentoEnum.MINUTAORDINANZA) &&
              data.deposito?.stato == StatoProvvedimentoEnum.RIFIUTATO && (
                <Grid item xs={12}>
                  <Typography variant="h3">
                    {t('shared.sideModal.stato')} {data.deposito?.stato} -{' '}
                    {t('shared.sideModal.utente')} {data.deposito?.utenteInCarico}{' '}
                    - {t('shared.sideModal.data')}{' '}
                    {formatDate(data.deposito?.dataRifiuto, 'DD/MM/YYYY')}
                  </Typography>
                </Grid>
              )}
            <Grid item mt={3} xs={12}>
              <NsButton
                size="small"
                sx={{ marginRight: '10px' }}
                onClick={() => openDetail(data)}
                variant="contained"
              >
                {t('shared.sideModal.apriDettaglio')}
              </NsButton>
              {(data.deposito?.idUtente == null ||
                (data.deposito?.idUtente &&
                  data.deposito?.idUtente != getUserData().idUtente)) &&
                data.deposito?.stato == StatoProvvedimentoDeskEnum.NEW && (
                  <>
                    <NsButton
                      disabled={
                        (data.deposito?.tipo ==
                          TipologiaProvvedimentoEnum.SENTENZA ||
                          data.deposito?.tipo ==
                            TipologiaProvvedimentoEnum.ORDINANZA) &&
                        pubblicazioneDisabled()
                      }
                      size="small"
                      onClick={() => handleModal(data)}
                      variant="contained"
                    >
                      {t('shared.sideModal.prendiCarico')}
                    </NsButton>
                    {(data.deposito?.tipo == TipologiaProvvedimentoEnum.SENTENZA ||
                      data.deposito?.tipo ==
                        TipologiaProvvedimentoEnum.ORDINANZA) &&
                      pubblicazioneDisabled() && (
                        <NsTooltip
                          title={t('shared.sideModal.pubblicazioneDisabled')}
                          placement="right"
                          icon={
                            <InfoIcon
                              sx={{
                                color: '#308A7D',
                                position: 'absolute',
                                marginTop: '8px',
                              }}
                            />
                          }
                        />
                      )}
                  </>
                )}
            </Grid>
            <Grid item mt={3} xs={12}>
              <Diagnosi idNrg={idNrgFromElab} idCat={data.id} rows={diagnosiRows} />
            </Grid>
            <Grid item mt={3} xs={12}>
              <FileBusta
                esiti={esitiForFileBusta} 
                origine={data.deposito?.origine} 
                idCatBusta={data.id} 
              />
            </Grid>
          </Grid>
          {data.deposito?.idUtente &&
            data.deposito?.idUtente == getUserData().idUtente &&
            data.deposito?.stato == StatoProvvedimentoDeskEnum.NEW && (
              <Grid mt={5} item container justifyContent="space-between">
                <NsButton
                  variant="contained"
                  onClick={() => rifiutaDeposito(data)}
                  color="error"
                >
                  {t('common.rifiuta')}
                </NsButton>
                <NsButton
                  variant="contained"
                  onClick={() => acceptDeposito(data)}
                >
                  {t('common.accetta')}
                </NsButton>
              </Grid>
            )}
        </Box>
        <DialogModal {...modalProps} />
      </Drawer>
    </>
  );
}
