import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  ClickAwayListener,
  Grid,
  Typography,
  useTheme,
} from '@mui/material';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { getUserData, pubblicazioneDisabled } from 'src/utils/Authentication';
import StatoProvvedimentoDeskEnum from '../enum/StatoProvvedimentoDeskEnum';
import StatoProvvedimentoEnum from '../enum/StatoProvvedimentoEnum';
import TipologiaProvvedimentoEnum from '../enum/TipologiaProvvedimentoEnum';
import { Esito, SidemodalData } from '../interfaces';
import {
  formatDate,
  getStyledModal,
  mapEsitiAndDiagnosi,
  tipoMap,
} from '../shared/Utils';
import AccettaModal from './AccettaModal';
import ApriCaricamento from './ApriCaricamento';
import DatiDepositi from './DatiDepositi';
import Diagnosi from './Diagnosi';
import FileBusta from './FileBusta';
import FirmaModal from './FirmaModal';
import RifiutaModal from './RifiutaModal';
import DialogModal from '../shared/DialogModal';

const stato = {
  marginLeft: '16px',
  background: '#D7E9E6',
  color: '#308A8E',
  fontSize: '15px',
  borderRadius: '5px',
  height: '20px',
  alignItems: 'center',
  padding: '0 10px 0 10px',
  marginTop: '5px',
};
export default function DetailDeposito() {
  const router = useRouter();
  const { id } = router.query;
  const theme: any = useTheme();
  const { notify } = useNotifier();
  const { t } = useTranslation();
  const [deposito, setDeposito] = useState<any>([]);
  const [diagnosi, setDiagnosi] = useState<any[]>([]);
  const [elaborazioneDeposito, setElaborazioneDeposito] = useState<any[]>([]);
  const [esiti, setEsiti] = useState<Esito[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [caricamentoVisibility, setCaricamentoVisibility] =
    useState<boolean>(true);
  const [state, setState] = useState<JSX.Element>(<></>);
  const [idNrg, setIdNrg] = useState<string>('');
  const [openTooltip, setOpenTooltip] = useState<boolean>(false);

  const tooltipClose = () => {
    setOpenTooltip(false);
  };

  const tooltipOpen = () => {
    setOpenTooltip(true);
  };

  const style = getStyledModal(theme);

  const closeModal = (value: any) => {
    //se value è false significa che si sta cliccando fuori la modale e non deve essere chiusa
    if (value !== false) {
      setModalProps({ ...modalProps, isOpen: false });
    }      
  };

  const firma = (azureToken?: string, firmaOtp?: boolean) => {
    let content, title;
    content = (
      <FirmaModal
        closeModal={closeModal}
        refreshData={refreshData}
        id={id}
        azureToken={azureToken}
        isFirmaOtp={firmaOtp}
      />
    );
    title = `Firma del documento`;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
    maxWidth: 'sm',
  });

  const columns: any[] = [
    {
      id: 'data',
      label: t('depositi.detailDeposito.dataEOra'),
    },
    {
      id: 'codice',
      label: t('depositi.detailDeposito.evento'),
    },
    {
      id: 'descrizione',
      label: t('depositi.detailDeposito.descrizione'),
    },
  ];

  const refreshData = () => {
    getData();
  };

  const handlePrendiInCarico = () => {
    setCaricamentoVisibility(false);
    getData();
  };

  const getData = async () => {
    if (id) {
      setIsLoading(true);
      try {
        const api = await APIWrapper;
        const elabDep = await api.call(
          'GET',
          `elaborazionedeposito/byIdDeposito?idDeposito=${id}`
        );
        if (!elabDep) {
          notify({
            type: 'error',
            message: 'Errore durante la creazione elaborazione deposito',
          });
        }
        setElaborazioneDeposito(elabDep);

        const data = await api.call(
          'GET',
          `depositi/esiti?idDeposito=${id}`
        );
        const { diagnosi } = mapEsitiAndDiagnosi(data);
        setDiagnosi(diagnosi);
        setIdNrg(elabDep.deposito.nrg);
        const dataEsiti = await api.call(
          'GET',
          `depositi/contenuti?idDeposito=${id}`
        );

        const esiti = dataEsiti.map((esito: any) => {
          const tipo = esito.tipo || '';
          return { ...esito, tipo: tipoMap[tipo] };
        });

        setEsiti(esiti);
        const deposito = await api.call(
          'GET',
          `depositi?idDeposito=${id}`
        );
        console.log('API Response deposito:', deposito);
        deposito.nrg =
          deposito?.numeroRicorso || deposito?.annoRicorso
            ? `${deposito?.numeroRicorso}/${deposito?.annoRicorso}`
            : '';

        setDeposito(deposito);
        setIsLoading(false);

        if (
          deposito?.tipo &&
          deposito?.stato === StatoProvvedimentoEnum.RIFIUTATO
        ) {
          setState(
            <Box display="flex" alignItems="baseline">
              <Box sx={stato} ml={2}>
                {t('common.rifiutato')}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {formatDate(deposito?.dataRifiutata, 'DD/MM/YYYY  [-] HH:mm')}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {deposito?.utenteInCarico}
              </Box>
            </Box>
          );
        }

        if (
          (deposito?.tipo === TipologiaProvvedimentoEnum.SENTENZA ||
            deposito?.tipo === TipologiaProvvedimentoEnum.ORDINANZA) &&
          deposito?.stato === StatoProvvedimentoEnum.ACCETTATO
        ) {
          setState(
            <Box display="flex" alignItems="baseline">
              <Box sx={stato} ml={2}>
                {t('common.pubblicato')}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {t('depositi.detailDeposito.nRaccGen')}{' '}
                {deposito?.numRaccGenerale}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {formatDate(
                  deposito?.dataAccettazione,
                  'DD/MM/YYYY  [-] HH:mm'
                )}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {deposito?.utenteInCarico}
              </Box>
            </Box>
          );
        }

        if (
          (deposito?.tipo == TipologiaProvvedimentoEnum.MINUTASENTENZA ||
            deposito?.tipo == TipologiaProvvedimentoEnum.MINUTAORDINANZA) &&
          deposito?.stato == StatoProvvedimentoEnum.ACCETTATO
        ) {
          setState(
            <Box display="flex" alignItems="baseline">
              <Box sx={stato} ml={2}>
                {t('common.accettato')}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {formatDate(
                  deposito?.dataAccettazione,
                  'DD/MM/YYYY  [-] HH:mm'
                )}
              </Box>
              <Box ml={1} sx={{ fontSize: '15px' }}>
                {deposito?.utenteInCarico}
              </Box>
            </Box>
          );
        }
      } catch (err: any) {
        setIsLoading(false);
        console.log('error', err);
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      await getData();
    };
    fetchData();
  }, [id]);

  const acceptDeposito = async () => {
    let content, title;
    content = (
      <AccettaModal
        oscuramentoDeskCsp={deposito.oscuramentoDeskCsp}
        firma={firma}
        tipoProvvedimento={deposito.tipo}
        closeModal={closeModal}
        id={id}
        refreshData={refreshData}
        elaborazioneDeposito={elaborazioneDeposito}
        origine={deposito?.origine}
      />
    );
    title = `Accettazione deposito fascicolo ${deposito.nrg}`;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const rifiutaDeposito = async () => {
    let content, title;
    content = (
      <RifiutaModal refreshData={refreshData} closeModal={closeModal} id={id} />
    );
    title = `Rifiuto deposito fascicolo ${deposito.nrg}`;
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const handleModal = (data: SidemodalData) => {
    data.id = deposito.idCat;
    setModalProps({
      ...modalProps,
      content: (
        <ApriCaricamento
          data={data}
          closeModal={closeModal}
          idUtente={deposito.idUtente}
          refreshData={() => handlePrendiInCarico()}
        />
      ),
      title: `Conferma presa in carico - fascicolo NRG ${data.nrg}`,
      isOpen: true,
    });
  };

  return isLoading ? (
    <NsFullPageSpinner isOpen={isLoading} value={100} />
  ) : (
    <>
      <Grid container>
        <Box display="flex" alignItems="center">
          <Link style={{ textDecoration: 'none' }} href="/depositi">
            <Typography
              variant="h4"
              sx={{ textDecoration: 'underline' }}
              color="primary"
            >
              {t('depositi.detailDeposito.gestioneDepositi')}
            </Typography>
          </Link>
          <Box ml={1} mr={1}>
            {'>'}
          </Box>
          <Typography variant="h4">
            {t('depositi.detailDeposito.dettaglioDepositoFascicolo')}
          </Typography>
        </Box>
        <Grid mt={2} item xs={12} container justifyContent="space-between">
          <Typography display="flex" variant="h1">
            {`Dettaglio deposito fascicolo ${deposito?.nrg}`}
            {state}
            {deposito?.deplano == "1" && (
              <Box component="span" sx={{ marginLeft: '8px', background: '#E0E0E0', color: '#666666', padding: '0 5px', borderRadius: '4px', fontSize: '14px', height: '20px', display: 'inline-flex', alignItems: 'center' }}>
                DE PLANO
              </Box>
            )}
          </Typography>
          <Box>
            {deposito?.idUtente &&
              deposito?.idUtente == getUserData().idUtente &&
              deposito.stato == StatoProvvedimentoDeskEnum.NEW && (
                <>
                  <NsButton
                    sx={{ mr: 2 }}
                    variant="contained"
                    onClick={rifiutaDeposito}
                    color="error"
                  >
                    {t('common.rifiuta')}
                  </NsButton>
                  <NsButton
                    sx={{ mr: 2 }}
                    variant="contained"
                    onClick={acceptDeposito}
                  >
                    {t('common.accetta')}
                  </NsButton>
                </>
              )}
            {caricamentoVisibility &&
              (deposito?.idUtente == null ||
                (deposito?.idUtente &&
                  deposito?.idUtente != getUserData().idUtente)) &&
              deposito.stato == StatoProvvedimentoDeskEnum.NEW && (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <NsButton
                    disabled={
                      (deposito?.tipo == TipologiaProvvedimentoEnum.SENTENZA ||
                        deposito?.tipo ==
                          TipologiaProvvedimentoEnum.ORDINANZA) &&
                      pubblicazioneDisabled()
                    }
                    size="small"
                    onClick={() => handleModal(deposito)}
                    variant="contained"
                  >
                    {t('depositi.prendiCarico')}
                  </NsButton>
                  {(deposito?.tipo == TipologiaProvvedimentoEnum.SENTENZA ||
                    deposito?.tipo == TipologiaProvvedimentoEnum.ORDINANZA) &&
                    pubblicazioneDisabled() && (
                      <ClickAwayListener onClickAway={tooltipClose}>
                        <NsTooltip
                          title={t('shared.sideModal.pubblicazioneDisabled')}
                          placement="bottom"
                          colorIcon="#308A7D"
                          icon={<InfoIcon />}
                        />
                      </ClickAwayListener>
                    )}
                </Box>
              )}
          </Box>
        </Grid>
        <Grid item xs={12} mb={2}>
          <Box display="flex">
            {deposito?.dataUdienza && (
              <Typography variant="h3" mr={1}>
                Udienza del {formatDate(deposito?.dataUdienza, 'D MMMM YYYY')}
              </Typography>
            )}
            {deposito?.ubicazione && (
              <Typography display="flex" variant="h3" mr={1}>
                <Box
                  sx={{
                    width: '20px',
                    height: '20px',
                    background: '#308A7D',
                    borderRadius: '50%',
                    mr: 1,
                  }}
                ></Box>
                {deposito?.ubicazione} |
              </Typography>
            )}
            {deposito?.tipoUdienza && (
              <Typography variant="h3" mr={1}>
                {deposito?.tipoUdienza} |
              </Typography>
            )}
            {deposito?.aula && (
              <Typography variant="h3" mr={1}>
                {deposito?.aula}
              </Typography>
            )}
          </Box>
        </Grid>
        <Grid xs={12} item md={5.9}>
          <Grid item p={3} container border={theme.custom.borders[0]} xs={12}>
            <DatiDepositi deposito={deposito} />
          </Grid>
          <Grid
            mt={2}
            item
            p={3}
            container
            border={theme.custom.borders[0]}
            mb={5}
            xs={12}
          >
            <Diagnosi idNrg={idNrg} idCat={id} rows={diagnosi} />
          </Grid>
        </Grid>
        <Grid item xs={12} md={0.2}></Grid>
        <Grid
          p={3}
          mb={5}
          item
          border={theme.custom.borders[0]}
          xs={12}
          md={5.9}
        >
          <FileBusta
            esiti={esiti}
            origine={deposito?.origine}
            idCatBusta={Number(id)}
          />
        </Grid>
      </Grid>
      <DialogModal {...modalProps} />
    </>
  );
}
