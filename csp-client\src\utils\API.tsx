import axios, { AxiosRequestConfig } from 'axios';
import * as Authentication from './Authentication';
import { useNotifier } from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import useDebouncedCallback from '@restart/hooks/useDebouncedCallback';
import { errorLogger, Severity } from './errorLogger';

export const AxiosInteceptor = () => {
  const { t, i18n } = useTranslation();
  const { notify } = useNotifier();

  const notificationUseDebounce = useDebouncedCallback(
    (value: { message: string; type: string }) => {
      notify({
        message: value.message,
        type: value.type as 'default' | 'error' | 'success' | 'warning' | 'info',
      });
    },
    1000
  );

  const Traduzione = (code: string, errorType: string) => {
    let key = 'server.errorCode.' + code;
    if (!i18n.exists(key)) {
      key = t('server.errorCode.traduzioneAssente', { code: code });
    } else {
      key = t(key);
    }
    notificationUseDebounce({
      message: t(key),
      type: errorType || 'error',
    });
  };

  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      // Create a safe error object without circular references
      let errorMessage = error?.response?.data?.message || error.message || 'Network Error';

      const errorDetails = {
        message: errorMessage,
        severity: Severity.ERROR,
        context: {
          status: error?.response?.status,
          statusText: error?.response?.statusText,
          url: error?.config?.url,
          method: error?.config?.method,
          data: error?.response?.data,
          // Don't include the full error object as it may contain circular references
          errorType: error?.name,
          errorCode: error?.code
        },
        component: 'AxiosInterceptor'
      };

      errorLogger.log(errorDetails);

      if (error.response?.status === 401) {
        Authentication.removeStoredToken();
        throw new Error('Unauthorized');
      } else if (error?.response?.status === 433) {
        Traduzione(
          error?.response?.data?.errorCode,
          error?.response?.data?.errorType.toLowerCase()
        );
        console.error(
          `Errore lato server: ${error.response.status.message}, codeError:${error.response.status.errorCode}`
        );
      } else if (error?.code === 'ERR_NETWORK') {
        notificationUseDebounce({
          message: t('server.errorCode.ERR_NETWORK'),
          type: 'error',
        });
      }
      return Promise.reject(error);
    }
  );
};

export default class API {
  private readonly username: string = 'cspclient';
  private readonly password: string = 'cspclient!';
  private cachedReponses: Map<string, any> = new Map();
  private readonly basePath: string = '/csp-backend/jsonapi/';

  constructor(
    urlServizi: string,
    username: string,
    password: string,
    basePath: string
  ) {
    this.username = username;
    this.password = password;
    this.basePath = urlServizi + basePath;
  }

  call = (
    method: AxiosRequestConfig['method'],
    endpoint: string,
    body?: any,
    cached?: boolean,
    responseType?: AxiosRequestConfig['responseType']
  ): Promise<any> => {
    if (cached && this.cachedReponses.has(endpoint)) {
      return Promise.resolve(this.cachedReponses.get(endpoint));
    }

    const token = Authentication.getStoredToken();
    const axiosConf: AxiosRequestConfig = {
      url: this.basePath + endpoint,
      method,
      auth: {
        username: this.username,
        password: this.password,
      },
      headers: {
        'content-type': 'application/json',
        'x-auth-token': token,
      },
      data: body,
      responseType: responseType,
    };

    return new Promise((resolve, reject) => {
      axios
        .request(axiosConf)
        .then((response) => {
          if (!response) {
            reject({
              type: 'error',
              message: 'Risposta non valida dal server',
            });
            return;
          }
          if (cached) {
            this.cachedReponses.set(endpoint, response.data);
          }
          resolve(response.data);
        })
        .catch((error) => {
          let errorMessage = "Errore durante l'interazione col server. ";
          let errorType = 'error';

          if (error.response) {
            errorMessage = error.response.data?.message || errorMessage + error;
            if (error.response.status === 400) {
              errorType = 'warning';
            }
          } else {
            errorMessage += error.message || error;
          }

          reject({ type: errorType, message: errorMessage });
        });
    });
  };

  download = (endpoint: string, body?: any): Promise<void> => {
    const token = Authentication.getStoredToken();
    const axiosConf: AxiosRequestConfig = {
      url: this.basePath + endpoint,
      method: body ? 'POST' : 'GET',
      responseType: 'blob',
      auth: {
        username: this.username,
        password: this.password,
      },
      headers: {
        'x-auth-token': token,
      },
      data: body,
    };

    return new Promise((resolve, reject) => {
      axios
        .request(axiosConf)
        .then((fileResponse) => {
          const contentType = fileResponse.headers['content-type'];
          const disposition: string = fileResponse.headers['content-disposition'];
          const fileName = disposition
            .split(';')[1]
            .split('=')[1]
            .replace(/"/g, '');

          const anchor = document.createElement('a');
          document.body.appendChild(anchor);

          const blob = new Blob([fileResponse.data], { type: contentType });
          const objectUrl = window.URL.createObjectURL(blob);

          anchor.href = objectUrl;
          anchor.download = fileName;
          anchor.click();

          window.URL.revokeObjectURL(objectUrl);
          resolve();
        })
        .catch((error) => {
          const errorMessage =
            error.response?.data?.message ||
            "Errore durante l'interazione col server. " + error;
          reject({ type: 'error', message: errorMessage });
        });
    });
  };

  downloadByteArray = (endpoint: string, body?: any): Promise<any> => {
    const token = Authentication.getStoredToken();
    const axiosConf: AxiosRequestConfig = {
      url: this.basePath + endpoint,
      method: body ? 'POST' : 'GET',
      responseType: 'blob',
      auth: {
        username: this.username,
        password: this.password,
      },
      headers: {
        'x-auth-token': token,
      },
      data: body,
    };

    return new Promise((resolve, reject) => {
      axios
        .request(axiosConf)
        .then((fileResponse) => {
          resolve(fileResponse.data);
        })
        .catch((error) => {
          const errorMessage =
            error.response?.data?.message ||
            "Errore durante l'interazione col server. " + error;
          reject({ type: 'error', message: errorMessage });
        });
    });
  };

  getBackendVersion = (): Promise<string> => {
    return this.call('GET', 'public/version');
  };
}
