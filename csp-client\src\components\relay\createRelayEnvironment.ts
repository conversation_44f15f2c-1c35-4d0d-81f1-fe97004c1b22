import {
  DefaultHandlerProvider,
  Environment,
  Network,
  RecordSource,
  Store,
} from 'relay-runtime';

import { loadQuery } from 'relay-hooks';

import axios from 'axios';
import { RecordMap } from 'relay-runtime/lib/store/RelayStoreTypes';
import { HandlerProvider } from 'relay-runtime/lib/handlers/RelayDefaultHandlerProvider';
import { update } from './connection';

let relayEnvironment: Environment;
let endpoint = '';

// TODO typings
async function fetchQuery(
  operation: any,
  variables: any,
  _cacheConfig: any,
  _uploadables: any
) {
  const serviceUrl = `${endpoint}/${'graphql'}`;

  return axios
    .post(
      serviceUrl,
      {
        query: operation.text, // GraphQL text from input
        variables,
      },
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      }
    )
    .then((response) => {
      if (response.status === 200) {
        return response.data;
      } else {
        // Gestisci sessione scaduta
        console.error(`${response.status}: ${response.statusText}`);
      }
    })
    .catch((err) => console.error(err));
}

type InitProps = {
  endpoint?: string;
  records?: any;
};

const handlerProvider: HandlerProvider = (handle: string) => {
  if (handle === 'connection_table') return { update };
  return DefaultHandlerProvider(handle);
};

const network = Network.create(fetchQuery);

export function createEnvironment(records: RecordMap, configName?: string) {
  const recordSource = new RecordSource(records);
  // TODO  metterlo a 100 per testare lo store
  const store = new Store(recordSource, { gcReleaseBufferSize: 5 });
  return new Environment({
    configName,
    network,
    store,
    handlerProvider,
  });
}

const prefetch = loadQuery();

export default function initEnvironment(
  options: InitProps = { endpoint: '' },
  name?: string
) {
  const { records = {} } = options;
  endpoint = options.endpoint || '';
  if (typeof window === 'undefined') {
    prefetch.dispose();
    return { environment: createEnvironment(records, name), prefetch };
  }

  // reuse Relay environment on client-side
  if (!relayEnvironment) {
    relayEnvironment = createEnvironment(records, name);
  }
  return { environment: relayEnvironment, prefetch };
}
