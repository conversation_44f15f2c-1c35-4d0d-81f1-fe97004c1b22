import { useEffect, useMemo } from 'react';
import { Box, Grid, MenuItem, Typography } from '@mui/material';
import { itIT } from '@mui/x-date-pickers';
import {
  NsDateCalendar,
  NsSelectAutocomplete,
} from '@netservice/astrea-react-ds';
import { useTranslation } from 'react-i18next';
import { getSiglaSezione } from 'src/utils/Authentication';
import { DatiUdienzaProps } from '../interfaces';
import {
  getCollegioOptions,
  getSezioniOptions,
  isRangeValid,
  tipoUdienzeOptions,
} from '../shared/Utils';
import { read } from 'fs';

const collegioOptions = getCollegioOptions();
const tipoUdienzaOptions = tipoUdienzeOptions();
const sezioniOptionsCE = getSezioniOptions();

export default function DatiUdienza({
  changedSezione,
  isSezioneSelected,
  searchedQuery,
  listaPresidenti,
  selectedSezione,
  onSezioneChange,
}: DatiUdienzaProps) {
  const { t } = useTranslation();
  const italianLocale = itIT.components.MuiLocalizationProvider.defaultProps.localeText;
  const siglaSezione = getSiglaSezione();
  const isCESection = siglaSezione === 'CE';

  useEffect(() => {
    if (!isCESection) {
      onSezioneChange({ value: siglaSezione });
      searchedQuery({ sezione: siglaSezione });
      changedSezione(true);
    }
  }, [isCESection, siglaSezione]);

  const tipoUdienzaItems = useMemo(
    () =>
      tipoUdienzaOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [tipoUdienzaOptions]
  );

  const sezioniItems = useMemo(
    () => (
      <MenuItem key={siglaSezione} value={siglaSezione}>
        {siglaSezione}
      </MenuItem>
    ),
    [siglaSezione]
  );

  const sezioniItemsCE = useMemo(
    () =>
      sezioniOptionsCE.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [sezioniOptionsCE]
  );

  const collegioItems = useMemo(
    () =>
      collegioOptions.map(({ value, label }) => (
        <MenuItem key={value} value={value}>
          {label}
        </MenuItem>
      )),
    [collegioOptions]
  );

  return (
    <Grid item xs={12}>
      <Box
        border={1}
        borderColor="grey.500"
        p={2}
        borderRadius={1}
        component={'fieldset'}
        sx={{
          gap: '20px',
          flexWrap: 'wrap',
          '& > div': {
            width: '100%',
            marginBottom: '10px',
          },
        }}
        display={'flex'}
      >
        <legend>
          <Typography sx={{ fontWeight: 'bold' }}>
            {t('monitoraggio.form.datiUdienza')}
          </Typography>
        </legend>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.sezione')}
              <Box component={'span'} sx={{ color: 'red' }}>
                *
              </Box>
            </Typography>
          </legend>
          <NsSelectAutocomplete
            key={`sezione-${selectedSezione}`}
            name="sezione"
            label={<Typography>{t('monitoraggio.form.numero')}</Typography>}
            size="small"
            disabled={!isCESection}
            changed={(value: any) => {
              const sezioneValue = value?.value || null;
              onSezioneChange(value);
              changedSezione(!!sezioneValue);
            }}
            defaultValue={selectedSezione ? { value: selectedSezione, label: selectedSezione } : null}
          >
            {isCESection ? sezioniItemsCE : sezioniItems}
          </NsSelectAutocomplete>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.dataUdienza')}
            </Typography>
          </legend>
          <Grid container spacing={4}>
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataUdienzaDa"
                label={<Typography>{t('monitoraggio.form.da')}</Typography>}
                name="dataUdienzaDa"
                errorMessage={[
                  t('form.errors.required', { field: 'Data Udienza Da' }),
                ]}
                disabled={!isSezioneSelected}
                size="small"
                onChange={() => {
                  searchedQuery({});
                }}
                inputProps={{ readOnly: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <NsDateCalendar
                localeText={italianLocale}
                key="dataUdienzaA"
                label={<Typography>{t('monitoraggio.form.a')}</Typography>}
                name="dataUdienzaA"
                validate={[isRangeValid]}
                dependsOn={['dataUdienzaDa']}
                errorMessage={[t('monitoraggio.form.validazioneDataUdienza')]}
                disabled={!isSezioneSelected}
                size="small"
                onChange={() => {
                  searchedQuery({});
                }}
                inputProps={{ readOnly: true }}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.tipoUdienza')}
            </Typography>
          </legend>
          <NsSelectAutocomplete
            key="tipoUdienza"
            name="tipoUdienza"
            label={<Typography>{t('monitoraggio.form.tipologia')}</Typography>}
            size="small"
            disabled={!isSezioneSelected}
            changed={(value:any) => {
              searchedQuery({ tipoUdienza: value?.label });
            }}
          >
            {tipoUdienzaItems}
          </NsSelectAutocomplete>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.collegio')}
            </Typography>
          </legend>
          <NsSelectAutocomplete
            key="collegio"
            name="collegio"
            label={<Typography>{t('monitoraggio.form.numero')}</Typography>}
            size="small"
            disabled={!isSezioneSelected}
            changed={(value:any) => {
              searchedQuery({ collegio: value?.value });
            }}
          >
            {collegioItems}
          </NsSelectAutocomplete>
        </Box>
        <Box
          border={1}
          borderColor="grey.300"
          p={2}
          borderRadius={1}
          component={'fieldset'}
          sx={{
            gap: '20px',
            flexWrap: 'wrap',
            '& > div': {
              width: '100%',
              marginBottom: '10px',
            },
          }}
        >
          <legend>
            <Typography sx={{ fontWeight: 'bold' }}>
              {t('monitoraggio.form.presidente')}
            </Typography>
          </legend>
          <NsSelectAutocomplete
            key="presidente"
            name="presidente"
            noOptionsText={t('monitoraggio.form.nessunPresidenteDisponibile')}
            sx={{ minWidth: 200 }}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: '300px',
                },
              },
            }}
            label={
              <Typography>{t('monitoraggio.form.nomeECognome')}</Typography>
            }
            size="small"
            disabled={!isSezioneSelected}
          >
            {listaPresidenti &&
              Object.entries(listaPresidenti).map(([cf, nome]: any) => (
                <MenuItem key={cf} value={cf}>
                  {nome}
                </MenuItem>
              ))}
          </NsSelectAutocomplete>
        </Box>
      </Box>
    </Grid>
  );
}
