import ClearIcon from '@mui/icons-material/Clear';
import ReplayIcon from '@mui/icons-material/Replay';
import {
  Box,
  Button,
  Checkbox,
  Grid,
  Menu,
  MenuItem,
  Typography,
  useTheme,
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { makeStyles } from '@mui/styles';
import { NsButton, useNotifier } from '@netservice/astrea-react-ds';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { formatDate, getStyledModal, tipoProvvedimentoMap } from '../shared/Utils';
import DepositoFilter from './DepositoFilter';
import DepositoTable from './DepositoTable';
import { getSiglaSezione } from 'src/utils/Authentication';
import { getDefaultBackendSorting, getFallbackSorting } from './constants/depositiSorting';
import DepositiEsporta from './DepositiEsporta';
import DialogModal from '../shared/DialogModal';

const useStyles = makeStyles((theme) => ({
  buttons: {
    height: '40px',
    '@media (max-width: 600px)': {
      marginBottom: '8px',
      width: '100%',
    },
  },
  buttonsContainer: {
    display: 'flex',
    gap: '16px',
    '@media (max-width: 600px)': {
      flexDirection: 'column',
      width: '100%',
    },
  },
  checkboxesContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '16px',
    '@media (max-width: 900px)': {
      flexDirection: 'column',
      marginBottom: '16px',
    },
  },
}));

interface FilteredItemProps {
  label: string;
  value: string | number;
}

const FilteredItem = ({ label, value }: FilteredItemProps) => (
  <Grid
    item
    xs={12}
    sm={6}
    md={4}
    lg={2}
    sx={{ mb: 2, pl: { xs: 1, sm: 2 }, display: 'flex', alignItems: 'center' }}
  >
    <Typography variant="h3">
      {label}: {value}
    </Typography>
  </Grid>
);

export default function Depositi() {
  const { t } = useTranslation();
  const classes = useStyles();
  const { notify } = useNotifier();
  const siglaSezione = getSiglaSezione();

  const [filteredRequestBody, setFilteredRequestBody] = useState<any>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [depositanteNC, setDepositanteNC] = useState<string>();
  const [loadDepositi, setLoadDepositi] = useState<number>(0);
  const rowToNumberTable = 25;
  const [magistrati, setMagistrati] = useState<[]>([]);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [sezione, setSezione] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: () => {
      setModalProps((prev) => ({ ...prev, isOpen: false }));
      setIsFilterModalOpen(false);
    },
    title: '',
    content: <></>,
    maxWidth: 'md',
  });

  const [checkboxes, setCheckboxes] = useState({
    daAccettare: false,
    daPrendereInCarico: false,
    minute: false,
    sentenzeOrdinanze: false,
  });

  const [isValidFilterCombination, setIsValidFilterCombination] =
    useState(false);

  const [filterType, setFilterType] = useState<'checkbox' | 'modal' | null>(null);

  const [checkboxFilters, setCheckboxFilters] = useState<any>({});
  const [modalFilters, setModalFilters] = useState<any>({});

  const [isFiltered, setIsFiltered] = useState<boolean>(false);

  const getIsFilteredValue = () => {
    if (!filteredRequestBody) return false;
    let numParams = Object.keys(filteredRequestBody.criteriaParameters).length;
    if (siglaSezione !== 'CE') numParams -= 1;
    return numParams > 0;
  };

  useEffect(() => {
    const fetchMagistrati = async () => {
      const api = await APIWrapper;
      const response = await api.call(
        'GET',
        `anagrafiche/magistratiPenaleAttivi`
      );
      setMagistrati(
        response.filter(
          (magistrato: any) => magistrato.codiceFiscale !== undefined
        )
      );
    };
    fetchMagistrati();
    setSezione(siglaSezione === 'CE' ? null : siglaSezione);
  }, [siglaSezione]);

  useEffect(() => {
    if (filterType !== 'checkbox') return;

    // Iniziamo con un oggetto vuoto invece di usare i filtri precedenti
    let criteria: any = {};

    if (sezione) {
      criteria.sezioneUdienza = sezione;
    }

    // Gestione separata per daAccettare e daPrendereInCarico
    if (checkboxes.daPrendereInCarico) {
      criteria.stato = 'NEW';
      criteria.idUtenteNull = true;
    } else if (checkboxes.daAccettare) {
      criteria.stato = 'NEW';
      // Non aggiungiamo idUtenteNull per daAccettare
    }
    // Se nessuna delle due checkbox è selezionata, non aggiungiamo il filtro stato

    // Gestione del tipo di provvedimento basata sulle checkbox
    if (checkboxes.minute) {
      criteria.tipiProvvedimento = ['MinutaSentenza', 'MinutaOrdinanza'];
    } else if (checkboxes.sentenzeOrdinanze) {
      criteria.tipiProvvedimento = ['Sentenza', 'Ordinanza'];
    }
    // Se nessuna delle due checkbox è selezionata, non aggiungiamo il filtro tipoProvvedimento

    const requestBody = {
      pageInfo: {
        pageNumber: 0,
        pageSize: rowToNumberTable,
        order: filteredRequestBody?.pageInfo?.order || getFallbackSorting(),
      },
      criteriaParameters: criteria,
    };

    setFilteredRequestBody(requestBody);
    setCheckboxFilters(criteria);
  }, [
    filterType,
    sezione,
    checkboxes,
    rowToNumberTable,
  ]);

  useEffect(() => {
    if (filterType !== 'modal') return;

    let criteria: any = { ...modalFilters };

    if (sezione) {
      criteria.sezioneUdienza = sezione;
    }

    const requestBody = {
      pageInfo: {
        pageNumber: 0,
        pageSize: rowToNumberTable,
        order: filteredRequestBody?.pageInfo?.order || getFallbackSorting(),
      },
      criteriaParameters: criteria,
    };

    setFilteredRequestBody(requestBody);
  }, [filterType, modalFilters, sezione, rowToNumberTable]);

  const handleCheckboxChange = (type: keyof typeof checkboxes) => {
    setIsLoading(true);
    
    // Quando usiamo i filtri checkbox, resettiamo i filtri modal
    setFilterType('checkbox');
    setModalFilters({});
    setDepositanteNC(undefined);

    // Crea una copia dello stato attuale delle checkbox
    const newCheckboxes = { ...checkboxes, [type]: !checkboxes[type] };

    // Se stiamo attivando daAccettare, disattiviamo daPrendereInCarico e viceversa
    if (type === 'daAccettare' && newCheckboxes.daAccettare) {
      newCheckboxes.daPrendereInCarico = false;
    } else if (
      type === 'daPrendereInCarico' &&
      newCheckboxes.daPrendereInCarico
    ) {
      newCheckboxes.daAccettare = false;
    }

    // Gestione delle checkbox minute e sentenzeOrdinanze
    if (type === 'minute' && newCheckboxes.minute) {
      newCheckboxes.sentenzeOrdinanze = false;
    } else if (
      type === 'sentenzeOrdinanze' &&
      newCheckboxes.sentenzeOrdinanze
    ) {
      newCheckboxes.minute = false;
    }

    // Aggiorna lo stato delle checkbox
    setCheckboxes(newCheckboxes);
  };

  const resetCheckboxFilters = () => {
    setCheckboxes({
      daAccettare: false,
      daPrendereInCarico: false,
      minute: false,
      sentenzeOrdinanze: false,
    });
    setCheckboxFilters({});
  };

  const openModal = (
    content: JSX.Element,
    title: string,
    maxWidth: 'md' | 'sm' = 'md'
  ) => {
    setModalProps({ ...modalProps, content, isOpen: true, title, maxWidth });
  };

  const handleModal = (type: string) => {
    switch (type) {
      case 'depositoFilter':
        setIsFilterModalOpen(true);

        // Se stiamo usando filtri checkbox, passiamo solo la struttura base
        // altrimenti passiamo i filtri modal esistenti
        const modalRequestBody = filterType === 'checkbox'
          ? {
              pageInfo: filteredRequestBody?.pageInfo || {
                pageNumber: 0,
                pageSize: rowToNumberTable,
                order: getFallbackSorting(),
              },
              criteriaParameters: sezione ? { sezioneUdienza: sezione } : {},
            }
          : filteredRequestBody;

        openModal(
          <DepositoFilter
            magistrati={magistrati}
            filteredRequestBody={modalRequestBody}
            setDepositanteNC={setDepositanteNC}
            setFilteredRequestBody={(newRequestBody) => {
              // Quando usiamo i filtri modal, resettiamo i filtri checkbox
              setFilterType('modal');
              resetCheckboxFilters();

              // Impostiamo i nuovi filtri modal
              setModalFilters(newRequestBody.criteriaParameters);
              setFilteredRequestBody(newRequestBody);

              modalProps.onClose();

              setTimeout(() => {
                setLoadDepositi((prev) => prev + 1);
              }, 100);
            }}
            resetCheckboxFilters={resetCheckboxFilters}
            closeModal={() => {
              modalProps.onClose();
            }}
          />,
          t('depositi.filtriElencoDepositi'),
          'md'
        );
        break;
      case 'esporta':
        openModal(
          <DepositiEsporta closeModal={modalProps.onClose} />,
          t('depositi.esportaDepositi'),
          'md'
        );
        break;
    }
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setLoadDepositi((prev) => prev + 1);
  };
  const handleReset = () => {
    setIsLoading(true);
    
    // Reset di tutti i filtri
    setFilterType(null);
    setModalFilters({});
    setCheckboxFilters({});

    setFilteredRequestBody({
      pageInfo: {
        pageNumber: 0,
        pageSize: rowToNumberTable,
        orderList: getDefaultBackendSorting(),
      },
      criteriaParameters: {},
    });

    setDepositanteNC(undefined);
    setCheckboxes({
      daAccettare: false,
      daPrendereInCarico: false,
      minute: false,
      sentenzeOrdinanze: false,
    });

    setLoadDepositi((prev) => prev + 1);
  };
  const handleActionClick = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget);
  const handleMenuClose = () => setAnchorEl(null);
  const executePrendiInCarico = async (
    setIsSubmitting: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    try {
      setIsSubmitting(true);
      const api = await APIWrapper;
      await api.call('POST', 'depositi/presaInCaricoMultipla', {
        idDepositi: selectedRows,
      });
      notify({ message: t('depositi.depositiPresiInCarico'), type: 'success' });
      setSelectedRows([]);
      handleRefresh();
    } catch (error: any) {
      notify({
        message:
          error.response?.data?.errorCode === 'DEPOSITO_GIA_ASSEGNATO'
            ? t('server.errorCode.DEPOSITO_GIA_ASSEGNATO')
            : error.response?.data?.message ||
              t('server.errorCode.PRESA_IN_CARICO_GENERIC_ERROR'),
        type: 'error',
      });
    } finally {
      handleMenuClose();
      modalProps.onClose();
    }
  };

  // Component for the modal content with its own state
  const ConfirmModalContent = () => {
    const [isSubmitting, setIsSubmitting] = useState(false);

    return (
      <Box>
        <Typography sx={{ mb: 2 }} variant="h3">
          {t('depositi.confirmPrendiInCarico')}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <NsButton
            onClick={modalProps.onClose}
            variant="outlined"
            disabled={isSubmitting}
          >
            {t('buttonsLabel.annulla')}
          </NsButton>
          <NsButton
            onClick={() => executePrendiInCarico(setIsSubmitting)}
            variant="contained"
            color="primary"
            disabled={isSubmitting}
          >
            {t('buttonsLabel.conferma')}
          </NsButton>
        </Box>
      </Box>
    );
  };

  const handlePrendiInCarico = () => {
    openModal(
      <ConfirmModalContent />,
      t('depositi.presaInCaricoMassiva'),
      'sm'
    );
  };

  useEffect(() => {
    setIsFiltered(getIsFilteredValue());
  }, [filteredRequestBody]);

  // Check if the filter combination is valid for presa in carico massiva
  useEffect(() => {
    const isDaPrendereInCaricoChecked = checkboxes.daPrendereInCarico;
    const isMinuteChecked = checkboxes.minute;
    const isSentenzeOrdinanzeChecked = checkboxes.sentenzeOrdinanze;

    // Valid combinations:
    // 1. Da prendere in carico + minute
    // 2. Da prendere in carico + sentenze/ordinanze
    const isValid =
      isDaPrendereInCaricoChecked &&
      (isMinuteChecked || isSentenzeOrdinanzeChecked);

    setIsValidFilterCombination(isValid);
  }, [checkboxes]);

  return (
    <Grid container>
      <Typography
        mt={2}
        variant="h1"
        sx={{
          fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
          mb: { xs: 2, sm: 3, md: 4 },
        }}
      >
        {t('depositi.gestioneDepositi')}
      </Typography>

      {isFiltered && (
        <Grid
          container
          sx={{
            mt: { xs: 2, sm: 3, md: 5 },
            p: { xs: 1, sm: 2 },
            border: '2px solid #308A7D',
            borderRadius: '10px',
            width: '100%',
            overflow: 'hidden',
          }}
        >
          <Typography
            variant="h3"
            sx={{
              ml: { xs: 1, sm: 2 },
              display: 'flex',
              justifyContent: 'center',
              minWidth: '125px',
              maxWidth: { xs: '100%', sm: '200px' },
              bgcolor: '#FEFEFE',
              px: 2,
              borderRadius: '4px',
            }}
          >
            {t('depositi.filtriImpostati')}
          </Typography>
          <Grid
            container
            sx={{
              justifyContent: 'flex-start',
              gap: { xs: 1, sm: 2 },
              mt: 2,
              px: { xs: 1, sm: 2 },
              flexWrap: 'wrap',
            }}
          >
            {filteredRequestBody?.criteriaParameters?.sezioneUdienza &&
              siglaSezione === 'CE' && (
                <FilteredItem
                  label={t('depositi.sezione')}
                  value={filteredRequestBody.criteriaParameters.sezioneUdienza}
                />
              )}
            {filteredRequestBody?.criteriaParameters?.tipiProvvedimento && (
              <FilteredItem
                label={t('depositi.depositoFilter.tipoProvvedimento')}
                value={
                  (filteredRequestBody.criteriaParameters.tipiProvvedimento.includes(
                    'MinutaSentenza'
                  ) &&
                  filteredRequestBody.criteriaParameters.tipiProvvedimento.includes(
                    'MinutaOrdinanza'
                  ))
                    ? 'Minute'
                    : (filteredRequestBody.criteriaParameters.tipiProvvedimento.includes(
                        'Sentenza'
                      ) &&
                      filteredRequestBody.criteriaParameters.tipiProvvedimento.includes(
                        'Ordinanza'
                      ))
                    ? 'Sentenza/Ordinanza'
                    : filteredRequestBody.criteriaParameters.tipiProvvedimento.length === 1
                    ? (filteredRequestBody.criteriaParameters.tipiProvvedimento[0] === 'MinutaOrdinanza'
                      ? 'Minuta di Ordinanza'
                      : filteredRequestBody.criteriaParameters.tipiProvvedimento[0] === 'MinutaSentenza'
                      ? 'Minuta di Sentenza'
                      : filteredRequestBody.criteriaParameters.tipiProvvedimento[0] === 'Ordinanza'
                      ? 'Ordinanza'
                      : filteredRequestBody.criteriaParameters.tipiProvvedimento[0] === 'Sentenza'
                      ? 'Sentenza'
                      : filteredRequestBody.criteriaParameters.tipiProvvedimento[0])
                    : filteredRequestBody.criteriaParameters.tipiProvvedimento.join(
                        ', '
                      )
                }
              />
            )}
            {filteredRequestBody?.criteriaParameters?.dataUdienzaA && (
              <FilteredItem
                label={t('depositi.dataUdienza')}
                value={formatDate(
                  filteredRequestBody.criteriaParameters.dataUdienzaA,
                  'DD/MM/YY'
                )}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.tipoUdienza && (
              <FilteredItem
                label={t('depositi.tipoUdienza')}
                value={filteredRequestBody.criteriaParameters.tipoUdienza}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.aula && (
              <FilteredItem
                label={t('depositi.collegio')}
                value={filteredRequestBody.criteriaParameters.aula}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.numeroRicorso && (
              <FilteredItem
                label={t('depositi.numeroRicorso')}
                value={filteredRequestBody.criteriaParameters.numeroRicorso}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.annoRicorso && (
              <FilteredItem
                label={t('depositi.annoRicorso')}
                value={filteredRequestBody.criteriaParameters.annoRicorso}
              />
            )}
            {depositanteNC && (
              <FilteredItem
                label={t('depositi.depositante')}
                value={depositanteNC}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.stato && (
              <FilteredItem
                label={t('depositi.stato')}
                value={
                  filteredRequestBody.criteriaParameters.idUtenteNull
                    ? t('depositi.daPrendereInCarico')
                    : filteredRequestBody.criteriaParameters.stato === 'NEW'
                    ? 'DA ACCETTARE'
                    : filteredRequestBody.criteriaParameters.stato
                }
              />
            )}
            {filteredRequestBody?.criteriaParameters?.tipoProvvedimento && (
              <FilteredItem
                label={t('depositi.atto')}
                value={tipoProvvedimentoMap[filteredRequestBody.criteriaParameters.tipoProvvedimento.toUpperCase()] || filteredRequestBody.criteriaParameters.tipoProvvedimento}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.dataDepositoA && (
              <FilteredItem
                label={t('depositi.dataDeposito')}
                value={formatDate(
                  filteredRequestBody.criteriaParameters.dataDepositoA,
                  'DD/MM/YY'
                )}
              />
            )}
            {filteredRequestBody?.criteriaParameters?.oscuramentoDeskCsp && (
              <FilteredItem
                label={t('depositi.oscuramentoDeskCsp')}
                value={
                  filteredRequestBody.criteriaParameters.oscuramentoDeskCsp ==
                  '1'
                    ? t('common.yes')
                    : t('common.no')
                }
              />
            )}
            {filteredRequestBody?.criteriaParameters?.anomalia && (
              <Grid
                item
                xs={2}
                mb={2}
                pl={2}
                display="grid"
                gridAutoFlow={'column'}
              >
                <Typography variant="h3">
                  {t('depositi.diagnosi')}:{' '}
                  {filteredRequestBody.criteriaParameters.anomalia == '1'
                    ? 'Warning'
                    : filteredRequestBody.criteriaParameters.anomalia == '2'
                    ? 'Error'
                    : 'Fatal'}
                </Typography>
              </Grid>
            )}
            <Grid
              item
              xs={12}
              sm="auto"
              sx={{
                display: 'flex',
                justifyContent: { xs: 'center', sm: 'flex-start' },
                pl: { xs: 1, sm: 2 },
                mt: { xs: 2, sm: 0 },
              }}
            >
              <Box
                sx={{
                  background: '#308a7d',
                  borderRadius: '10px',
                  minWidth: { xs: '200px', sm: '79px' },
                  color: 'white',
                  marginBottom: '5px',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0 8px',
                  cursor: 'pointer',
                  height: '30px',
                  display: 'flex',
                  gap: 1,
                }}
                onClick={handleReset}
              >
                <ClearIcon fontSize="small" />
                {t('common.reset')}
              </Box>
            </Grid>
          </Grid>
        </Grid>
      )}
      <Grid
        item
        xs={12}
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 2, md: 0 },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', md: 'center' },
          mb: { xs: 2, sm: 3 },
          marginTop: '2rem',
        }}
      >
        <Typography
          variant="h1"
          sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } }}
        >
          {t('depositi.elencoDepositi')}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', lg: 'row' },
            gap: 2,
            width: { xs: '100%', lg: 'auto' },
          }}
        >
          <Box className={classes.checkboxesContainer}>
            {[
              'daAccettare',
              'daPrendereInCarico',
              'minute',
              'sentenzeOrdinanze',
            ].map((type) => (
              <Typography
                key={type}
                variant="h3"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                <Checkbox
                  checked={checkboxes[type as keyof typeof checkboxes]}
                  onClick={() =>
                    handleCheckboxChange(type as keyof typeof checkboxes)
                  }
                />
                {t(`depositi.${type}`)}
              </Typography>
            ))}
          </Box>
          <Box className={classes.buttonsContainer}>
            <NsButton
              onClick={handleActionClick}
              variant="contained"
              className={classes.buttons}
              size="small"
              disabled={selectedRows.length === 0 || !isValidFilterCombination}
              endIcon={<ArrowDropDownIcon />}
              sx={{
                width: { xs: '100%', lg: 'auto' },
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                minWidth: '150px',
                justifyContent: 'space-between',
              }}
              title={
                !isValidFilterCombination
                  ? t('depositi.presaInCaricoMassivaDisabled')
                  : ''
              }
            >
              {t('depositi.azioniMassive')}
            </NsButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              sx={{ '& .MuiPaper-root': { width: { xs: '100%', sm: 'auto' } } }}
            >
              <MenuItem onClick={handlePrendiInCarico}>
                {t('depositi.prendiInCarico')}
              </MenuItem>
            </Menu>
            <NsButton
              onClick={handleRefresh}
              variant="contained"
              className={classes.buttons}
              size="small"
              sx={{ width: { xs: '100%', lg: 'auto' } }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  justifyContent: 'center',
                }}
              >
                {t('common.aggiorna')}
                <ReplayIcon fontSize="small" />
              </Box>
            </NsButton>
            <NsButton
              onClick={() => handleModal('depositoFilter')}
              variant="contained"
              className={classes.buttons}
              size="small"
              sx={{ width: { xs: '100%', lg: 'auto' } }}
            >
              {t('common.filtra')}
            </NsButton>
            <NsButton
              onClick={() => handleModal('esporta')}
              variant="contained"
              className={classes.buttons}
              size="small"
              sx={{ width: { xs: '100%', lg: 'auto' } }}
            >
              {t('common.esporta')}
            </NsButton>
          </Box>
        </Box>
      </Grid>
      {!isFilterModalOpen && (
        <DepositoTable
          loadDepositi={loadDepositi}
          filteredRequestBody={filteredRequestBody}
          setParentLoading={setIsLoading}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
        />
      )}
      <DialogModal {...modalProps} />
    </Grid>
  );
}
