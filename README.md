## Frontend web del CSP

### Documentazione di progetto

* [Architettura](/doc/architettura_cass_penale-Architettura.jpg)
* [Risorse utili](/doc/risorse_formazione.md) per la formazione (guide, tutorial, articoli...)
* [Best practices](/doc/best_practices.md)

### Build 
- Installare Node.js (>= `16.15.0 )  
- Installare `yarn` (>= `1.22.19`) con `npm install -g yarn`
- Clonare il repository
- Entrare nella cartella del progetto   
- Eseguire `yarn install` per installare le dipendenze
- Fare una copia locale di .env.example e chiamarla .env. Si può modificare in locale la configurazione in base alle esigenze di sviluppo.
- Avviare il progetto in con `yarn dev`

### Gestione librerie

TODO ...

### Gestione stringhe

Le stringhe di testo dell'applicativo vanno inserite in public/locales/it/translation.json. Usare l'hook `useTranslation()` nel codice applicativo per caricare le traduzioni.

### Gestione stato

TODO

### Invocazione servizi graphql

TODO

### Login

Login in locale come nel CSP Client:

- http://localhost:3000 AMMINISTRATORE.NS / Password!
