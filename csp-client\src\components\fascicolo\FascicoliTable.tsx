import { FileDownloadOff } from '@mui/icons-material';
import DownloadIcon from '@mui/icons-material/Download';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { Box, Typography, useTheme } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import { makeStyles } from '@mui/styles';
import {
  NsFullPageSpinner,
  NsButton,
  NsTooltip,
  useNotifier,
} from '@netservice/astrea-react-ds';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from '../../utils/APIWrapper';
import StatoProvvedimentoDeskEnum from '../enum/StatoProvvedimentoDeskEnum';
import { Column, FascicoliTableProps, PageInfoProps } from '../interfaces';
import DetailParti from './DetailParti';
import DetailReati from './DetailReati';
import DetailRiuniti from './DetailRiuniti';
import DialogModal from '../shared/DialogModal';
import DataTableWithPagination from '../shared/DataTableWithPagination';

const iconBoxStyles = {
  background: '#e0eeec',
  width: '30px',
  height: '30px',
  marginRight: '10px',
  cursor: 'pointer',
  alignItems: 'center',
  justifyContent: 'center',
  display: 'flex',
};

const modalStyle = {
  position: 'absolute' as const,
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 2,
};

const actionIcons = [
  {
    id: 1,
    name: 'PreviewNonOscurato',
    typeFunction: 'PREVIEW',
    forOscurato: false,
    icon: (
      <NsTooltip
        title="Visualizza provvedimento pubblicato"
        icon={<RemoveRedEyeIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
      />
    ),
  },
  {
    id: 2,
    name: 'FileDownloadIcon',
    typeFunction: 'DOWNLOAD',
    forOscurato: false,
    icon: (
      <NsTooltip
        title="Download provvedimento pubblicato"
        icon={<DownloadIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
      />
    ),
  },
  {
    id: 3,
    name: 'PreviewOscurato',
    typeFunction: 'PREVIEW',
    forOscurato: true,
    icon: (
      <NsTooltip
        title="Visualizza provvedimento pubblicato oscurato"
        icon={<VisibilityOffIcon fontSize="small" sx={{ color: '#2E5A60' }} />}
      />
    ),
  },
  {
    id: 4,
    name: 'FileDownloadOscuratoIcon',
    typeFunction: 'DOWNLOAD',
    forOscurato: true,
    icon: (
      <NsTooltip
        title="Download provvedimento pubblicato oscurato"
        icon={<FileDownloadOff fontSize="small" sx={{ color: '#2E5A60' }} />}
      />
    ),
  },
];

export default function FascicoliTable({ 
  result, 
  pageInfoServer, 
  actionApplyPagination,
  actionApplySorting
}: Readonly<FascicoliTableProps>) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [content, setContent] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { notify } = useNotifier();
  const [modalProps, setModalProps] = useState({
    isOpen: false,
    title: '',
    content: <></>,
    style: modalStyle,
    onClose: () => { setModalProps({ ...modalProps, isOpen: false }); },
  });

  const useStyles = makeStyles({
    tooltip: {
      backgroundColor: 'black',
      fontSize: '14px',
    },
  });

  const classes = useStyles();

  const previewPDF = async (idCat: string) => {
    try {
      const api = await APIWrapper;
      setIsLoading(true);
      const blob = await api.downloadByteArray(
        `atti/download/?idCat=${idCat}`
      );
      setIsLoading(false);
      window.open(URL.createObjectURL(blob));
    } catch (err: any) {
      setIsLoading(false);
      notify({ type: 'error', message: err.message });
      console.log('error', err);
    }
  };

  const downloadAtto = async (id: string) => {
    try {
      const api = await APIWrapper;
      setIsLoading(true);
      await api.download(`atti/download/?idCat=${id}`);
      setIsLoading(false);
    } catch (err: any) {
      setIsLoading(false);
      notify({ type: 'error', message: err.message });
      console.log('error', err);
    }
  };

  useEffect(() => {
    if (result) {
      setContent(result);
    }
  }, [result]);

  const handlePagination = useCallback((pageInfo: PageInfoProps) => {
    actionApplyPagination(pageInfo);
  }, [actionApplyPagination]);

  const handleSorting = useCallback((property: string) => {
    actionApplySorting(property);
  }, [actionApplySorting]);

  const handleModal = async (param: string, data?: any) => {
    let content, title;
    const api = await APIWrapper;

    if (param == 'reati') {
      const reati = await api.call('GET', `ricorsi/reati?nrg=${data}`);
      if (reati.error) {
        notify({ type: 'error', message: reati.error.message });
      }

      content = <DetailReati reati={reati} />;
      title = t('fascicoli.fascicoliTable.visualizzazioneReati');
      modalStyle.width = 600;
    } else if (param == 'parti') {
      const parti = await api.call('GET', `ricorsi/parti?nrg=${data}`);
      if (parti.error) {
        notify({ type: 'error', message: parti.error.message });
      }

      content = <DetailParti parti={parti} />;
      title = t('fascicoli.fascicoliTable.visualizzazioneParti');
      modalStyle.width = 600;
    } else if (param == 'riuniti') {
      content = <></>;
      modalStyle.width = 600;
      title = `(R)- Riunito al ${data.numeroPadre}/${data.annoPadre}`;
    } else if (param == 'allRiuniti') {
      content = <DetailRiuniti idRicUdienza={data.idRicUdienza} />;
      title = t('fascicoli.fascicoliTable.visualizzazioneRiuniti');
      modalStyle.width = 600;
    } else {
      content = <></>;
      modalStyle.width = 600;
      title = `Parti fascicolo ${data.ricorso.numero || ''}/${
        data.ricorso.anno || ''
      }`;
    }

    setModalProps({ ...modalProps, content, isOpen: true, title });
  };
  
  const renderDataUdienza = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.dataudienza}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderRuolo = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.ruolo || 'NON ANCORA AL RUOLO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderEsitoUdienza = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.esitoUdienza || 'NON ESITATA'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderOrdine = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.numOrd}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderSemplificata = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.semplificata == '1' ? 'SI' : 'NO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderTipologia = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.tipoProvvedimento}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderOscuramentoSic = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.oscuramentoSic ? 'SI' : 'NO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderOscuramentoDeskCsp = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.oscuramentoDeskCsp ? 'SI' : 'NO'}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  function renderNrg(cell: any, row: any) {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>
            <Typography variant="body2" color="text.primary">
              {row.nrg}{' '}
            </Typography>
          </Box>
          <Box>
            {row.riunito && (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() => handleModal('riuniti', row?.ricercaRiunitiView)}
              >
                (R)
              </NsButton>
            )}
            {row?.ricercaRiunitiView?.principale ? (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() =>
                  handleModal('allRiuniti', row?.ricercaRiunitiView)
                }
              >
                {t('common.principale')}
              </NsButton>
            ) : (
              ''
            )}
          </Box>
        </Box>
      </TableCell>
    );
  }

  const handleRoute = (
    idOscuratoONonOscurato: string,
    previewOrDownload: string
  ) => {
    if (previewOrDownload === 'PREVIEW') {
      previewPDF(idOscuratoONonOscurato);
    } else if (previewOrDownload === 'DOWNLOAD') {
      downloadAtto(idOscuratoONonOscurato);
    }
  };
  const renderigngButton = (row: any, iconDaInserire: any) => {
    if (
      !iconDaInserire.forOscurato ||
        (iconDaInserire.forOscurato == row.oscuramentoDeskCsp && row.origine == 'SYSTEM')
    ) {
      return (
        <Box
          onClick={() =>
            handleRoute(
              iconDaInserire.forOscurato
                ? row.attoOscurato
                : row.attoNonOscurato,
              iconDaInserire.typeFunction
            )
          }
          key={iconDaInserire.id}
          sx={{ ...iconBoxStyles }}
        >
          {iconDaInserire.icon}
        </Box>
      );
    }
    return <></>;
  };

  const renderAzioni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.statoProvvedimento === StatoProvvedimentoDeskEnum.PUBBLICATA && (
          <Box display="flex">
            {actionIcons?.map((icon) => renderigngButton(row, icon))}
          </Box>
        )}
      </TableCell>
    );
  };

  const renderParti = (cell: any, row: any) => {
    const partePrincipale = row.detParti;
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>
            <Typography variant="body2" color="text.primary">
              {partePrincipale}
            </Typography>
          </Box>
          <Box>
            <NsButton
              sx={theme.custom.secondaryButton}
              onClick={() => handleModal('parti', row.id)}
            >
              {t('common.vedi')}
            </NsButton>
          </Box>
        </Box>
      </TableCell>
    );
  };

  const renderValore = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.primary">
            {row.valPond}
          </Typography>
        </Box>
      </TableCell>
    );
  };

  const renderHeadStato = (name: string) => {
    return <Box ml={1}>{name}</Box>;
  };
  const renderStato = (column: any, row: any) => {
    let state = row.stato;
    if (row.riunito) {
      state = '-';
    }
    return (
      <TableCell
        key={column.id}
        align={column.align}
        sx={{
          border: theme.custom.borders[0],
          background: row?.color ? row.color : '',
          color: row?.color == 'black' ? 'white' : '',
        }}
      >
        {state}
      </TableCell>
    );
  };

  const renderHeadAzioni = (name: string) => {
    return <Box>{name}</Box>;
  };

  const renderReato = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>
            <Typography variant="body2" color="text.primary">
              {row.reato}
            </Typography>
          </Box>
          <Box>
            <NsButton
              sx={theme.custom.secondaryButton}
              onClick={() => handleModal('reati', row.id)}
            >
              {t('common.vedi')}
            </NsButton>
          </Box>
        </Box>
      </TableCell>
    );
  };

  const columns: Column[] = [
    {
      id: 'numOrd',
      minWidth: 170,
      label: t('calendario.ordine') as string,
      align: 'left',
      render: renderOrdine,
    },
    {
      id: 'dataUd',
      minWidth: 170,
      label: t('calendario.dataUdienza') as string,
      align: 'left',
      render: renderDataUdienza,
    },
    {
      id: 'ruolo',
      minWidth: 170,
      label: t('fascicoli.ruolo') as string,
      align: 'left',
      render: renderRuolo,
      sortable: false,
    },
    {
      id: 'esitoUdienza',
      minWidth: 170,
      label: t('fascicoli.esitoUdienza') as string,
      align: 'left',
      render: renderEsitoUdienza,
      sortable: false,
    },
    {
      id: 'nrg',
      align: 'left',
      label: t('calendario.nrg') as string,
      minWidth: 170,
      render: renderNrg,
    },
    {
      id: 'detParti',
      minWidth: 170,
      label: t('calendario.parti') as string,
      align: 'left',
      render: renderParti,
      sortable: false,
    },
    {
      id: 'reato',
      minWidth: 170,
      label: t('calendario.reato') as string,
      align: 'left',
      render: renderReato,
      sortable: false,
    },
    {
      id: 'valPond',
      minWidth: 170,
      label: t('calendario.valorePonderale') as string,
      align: 'right',
      render: renderValore,
      sortable: false,
    },
    {
      id: 'tipoProvvedimento',
      minWidth: 170,
      label: t('calendario.tipologia') as string,
      align: 'left',
      render: renderTipologia,
      sortable: false,
    },
    {
      id: 'semplificata',
      minWidth: 170,
      label: t('calendario.semplificata') as string,
      align: 'left',
      render: renderSemplificata,
    },
    {
      id: 'statoProvvedimento',
      minWidth: 170,
      label: t('calendario.stato') as string,
      align: 'left',
      renderHeadCell: renderHeadStato,
      render: renderStato,
      sortable: false,
    },
    {
      id: 'oscuramentoSic',
      minWidth: 170,
      label: t('calendario.oscuramentoSic') as string,
      align: 'left',
      render: renderOscuramentoSic,
    },
    {
      id: 'oscuramentoDeskCsp',
      minWidth: 170,
      label: t('calendario.oscuramentoDeskCsp') as string,
      align: 'left',
      render: renderOscuramentoDeskCsp,
    },
    {
      id: 'numRaccGenerale',
      minWidth: 170,
      label: t('depositi.numRaccGen') as string,
      align: 'left',
    },
    {
      id: 'azioni',
      minWidth: 170,
      label: t('calendario.azioni') as string,
      align: 'left',
      renderHeadCell: renderHeadAzioni,
      render: renderAzioni,
      sortable: false,
    },
  ];

  return (
    <>
      <DataTableWithPagination
        rows={content}
        columns={columns}
        actionChangePage={handlePagination}
        pageInfoServer={pageInfoServer}
        sorting={true}
        createSortHandler={handleSorting}
        addPaginate={true}
      />
      <DialogModal {...modalProps} />
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
    </>
  );
}
