const sessionName = "msalSession";

export const Msal = {
    sessionOn: () => localStorage.setItem(sessionName, "on"),
    sessionOff: () => localStorage.removeItem(sessionName),
    session: () => sessionName in localStorage,
    getLogoutUrl: () => {
        const azureAdB2cHostName = "auth03coll.giustizia.it";
        const azureAdB2cTenantName = "b2cmingiustiziaspidcoll";
        const azureAdB2cPrimaryUserFlow = "B2C_1A_SIGNIN_AAD";
        const microsoftTenantId = "792bc8b1-9088-4858-b830-2aad443e9f3f";
        
        return `https://${azureAdB2cHostName}/${azureAdB2cTenantName}.onmicrosoft.com/${azureAdB2cPrimaryUserFlow}/oauth2/v2.0/logout?post_logout_redirect_uri=https://login.microsoftonline.com/${microsoftTenantId}/oauth2/v2.0/logout`;
    },
    popLogout: () => {
        Msal.sessionOff();
        window.open(Msal.getLogoutUrl(), "_blank");
    }
}