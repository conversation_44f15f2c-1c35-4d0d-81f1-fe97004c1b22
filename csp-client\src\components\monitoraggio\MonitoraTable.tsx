import EmailIcon from '@mui/icons-material/Email';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import {
  Box,
  Grid,
  Typography,
  styled,
  useTheme
} from '@mui/material';
import TableCell from '@mui/material/TableCell';
import {
  NsFullPageSpinner,
  NsAccordion,
  NsButton,
  NsAccordionDetails,
  NsTooltip,
  useNotifier
} from '@netservice/astrea-react-ds';
// import { NsAccordionProps } from '@netservice/astrea-react-ds/dist/cjs/components/components/NsAccordion';
import moment from 'moment';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import { Column } from '../interfaces';
import RelayTable from '../shared/RelayTable';
import { stateMap } from '../shared/Utils';
import DetailParti from '../fascicolo/DetailParti';
import DialogModal from '../shared/DialogModal';

export default function MonitoraTable({ data }: { data: any[] }) {
  const { t } = useTranslation();
  const theme: any = useTheme();

  const [rows, setRows] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [infoDate, setInfoDate] = useState<any[]>([]);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState<boolean>(true);
  const { notify } = useNotifier();

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
  });

  const router = useRouter();
  const getDateProvvedimento = async (
    idProvvedimento: any,
    idSentenza: any
  ) => {
    const api = await APIWrapper;
    const dateProvvedimento = await api.call(
      'GET',
      `monitoraggio/infoDate/${idProvvedimento}/${idSentenza}`
    );
    return dateProvvedimento;
  };

  const setAllInfoDate = async () => {
    setIsLoading(true);

    try {
      // Filter out rows with null idProvvedimento before processing
      const validRows = data.filter(row => {
        if (!row.idProvvedimento) {
          console.log('Row skipped - missing idProvvedimento:', {
            nrg: `${row.numeroRicorso}/${row.annoRicorso}`,
            stato: row.stato
          });
          return false;
        }
        return true;
      });

      console.log(`Processing ${validRows.length} of ${data.length} rows (${data
        .filter((row: any) => row.idProvvedimento)
        .length - validRows.length} skipped)`);

      const infoDatePromises = validRows.map(async (row) => {
        try {
          const dateProvvedimento = await getDateProvvedimento(
            row.idProvvedimento,
            row.idSentenza
          );
          return {
            dateProvvedimento,
            idRicUdien: row.idRicUdien,
          };
        } catch (error) {
          console.error('Error fetching date for row:', {
            nrg: `${row.numeroRicorso}/${row.annoRicorso}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          return null;
        }
      });

      const results = await Promise.all(infoDatePromises);
      const newInfoDate = results.filter(result => result !== null);

      if (JSON.stringify(newInfoDate) !== JSON.stringify(infoDate)) {
        setInfoDate(newInfoDate);
      }
    } catch (error) {
      console.error('Error in setAllInfoDate:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateData = () => {
    if (!data || data.length < 1) return;
    setAllInfoDate();
    const rows = data.map((row: any) => {
      const pubblicataSIC = row.stato === stateMap.PUBBLICATO_SIC;

      const dateProvvedimento = infoDate.find(
        (info) =>
          row.idRicUdien &&
          info.idRicUdien &&
          row.idRicUdien === info.idRicUdien
      )?.dateProvvedimento;
      if (dateProvvedimento) {
        let pubblicata = dateProvvedimento.PUBBLICATA;
        const dataPubblicazioneSIC = dateProvvedimento.PUBBLICATO_SIC;
        const pubblicazioneAlterataDalSic = dataPubblicazioneSIC ? true : false;
        if (pubblicazioneAlterataDalSic) {
          pubblicata = dataPubblicazioneSIC;
        }

        row = {
          ...row,
          dataMinuta: dateProvvedimento.DATA_MINUTA,
          dataMinutaSic: dateProvvedimento.DATA_MINUTA_SIC,
          pubblicazioneAlterataDalSic:
            pubblicazioneAlterataDalSic && pubblicataSIC,
          dataVisionePresidente: dateProvvedimento.MESSA_DISPOSIZIONE_PRE,
          pubblicata: pubblicata,
          pubblicataSic: dateProvvedimento.PUBBLICATO_SIC,
          ultimaMinutaModifica: dateProvvedimento.ULTIMA_MINUTA_MODIFICA,
          depositoProvvedimento: dateProvvedimento.DEPOSITO_PROVV,
          depositoProvvedimentoSic: dateProvvedimento.PROVV_DEPOSITATO_SIC,
          ultimaModifica: dateProvvedimento.ULTIMA_MODIFICA,
          ricorsoDaRedigere: 'RICORSO_DA_REDIGERE' in dateProvvedimento,
          minutaDepositataSic:
            'RICORSO_MINUTA_DEPOSITATA_SIC' in dateProvvedimento,
          ricorsoPubblicatoSic: 'RICORSO_PUBBLICATO_SIC' in dateProvvedimento,
        };
      }
      return row;
    });

    setRows(rows);
  };

  useEffect(updateData, [data, infoDate]);

  const FlexBox = styled(Box)({
    display: 'flex',
    alignItems: 'center',
  });

  const MonTableCell = styled(TableCell)({
    border: theme.custom.borders[0],
  });

  const renderUdienza = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row.sezione}, {moment(row.dataUdienza).format('DD/MM/YYYY')},{' '}
        {row.tipoUdienza}, {row.collegio}
      </MonTableCell>
    );
  };

  const renderPresidente = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row.presidente}
      </MonTableCell>
    );
  };

  const renderRelatore = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row.relatore}
      </MonTableCell>
    );
  };

  const renderEstensore = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row.estensore}
      </MonTableCell>
    );
  };

  const renderParti = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>
            {row.parti}
          </Box>
          <Box>
            <NsButton
              sx={theme.custom.secondaryButton}
              onClick={() => handleModal(row.nrg)} variant="text"
            >
              {t('common.vedi')}
            </NsButton>
          </Box>
        </Box>
      </TableCell>
    );
  };

  const handleModal = async (data: any) => {
    const api = await APIWrapper;
    const parti = await api.call('GET', `monitoraggio/parti?nrg=${data}`);
    if (parti.error) {
      notify({ type: 'error', message: parti.error.message });
    }
    const content = <DetailParti parti={parti} />;
    const title = t('monitoraggio.monitoraggioTable.visualizzazioneParti');

    setModalProps({ ...modalProps, content, isOpen: true, title });
  }

  const renderNrg = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row.numeroRicorso}/{row.annoRicorso}
      </MonTableCell>
    );
  };



  const renderDataMinuta = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row?.dataMinuta || row?.dataMinutaSic ? (
          <Box>
            {moment(row.dataMinutaSic || row.dataMinuta).format(
              'DD/MM/YYYY'
            )}
          </Box>
        ) : (
          ''
        )}
      </MonTableCell>
    );
  };

  const renderDataVisionePresidente = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row?.dataVisionePresidente
          ? moment(row.dataVisionePresidente).format('DD/MM/YYYY HH:mm')
          : ''}
      </MonTableCell>
    );
  };

  const renderDataRichiestaUltimaModifica = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row?.ultimaModifica
          ? moment(row.ultimaModifica).format('DD/MM/YYYY HH:mm ')
          : ''}
      </MonTableCell>
    );
  };

  const renderDataUltimaMinutaModificata = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row?.ultimaMinutaModifica
          ? moment(row.ultimaMinutaModifica).format('DD/MM/YYYY HH:mm')
          : ''}
      </MonTableCell>
    );
  };

  const renderDataDepositoProvvedimento = (cell: any, row: any) => {
    return (
      <MonTableCell key={cell.id} align={cell.align}>
        {row?.depositoProvvedimento || row?.depositoProvvedimentoSic ? (
          <Box>
            {row?.depositoProvvedimentoSic ? (
              <>
                {moment(row.depositoProvvedimentoSic).format('DD/MM/YYYY')}
              </>
            ) : (
              <>
                {moment(row.depositoProvvedimento).format(
                  'DD/MM/YYYY HH:mm'
                )}
              </>
            )}
          </Box>
        ) : (
          ''
        )}
      </MonTableCell>
    );
  };

  const renderDataPubblicazione = (cell: any, row: any) => {
    let date = null;
    if (row?.pubblicata) {
      date = row.pubblicataSic
        ? moment(row.pubblicataSic).format('DD/MM/YYYY')
        : moment(row.pubblicata).format('DD/MM/YYYY');
    }

    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {date ? (
            <Box>
              {date}
            </Box>
          ) : (
            ''
          )}
        </Box>
      </TableCell>
    );
  };

  const renderTotaleGiorni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
        }}
      >
        {row.totaleGiorni}
      </TableCell>
    );
  };

  const renderNumeroRaccoltaGenerale = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.nraccg}
      </TableCell>
    );
  };

  const renderAzioni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{
          border: theme.custom.borders[0],
        }}
      >
        {row.idCat && (
          <NsTooltip
            title={t('monitoraggio.monitoraTable.vaiUltimaBustaDepositata')}
            icon={<EmailIcon onClick={() => router.push(`/depositi/${row.idCat}`)} />}
          />
        )}
      </TableCell>
    );
  };



  const columns: Column[] = [
    {
      id: 'udienza',
      minWidth: 120,
      label: t('monitoraggio.udienza') as string,
      align: 'left',
      render: renderUdienza,
    },
    {
      id: 'presidente',
      minWidth: 120,
      label: t('monitoraggio.presidente') as string,
      align: 'left',
      render: renderPresidente,
    },
    {
      id: 'relatore',
      minWidth: 120,
      label: t('monitoraggio.relatore') as string,
      align: 'left',
      render: renderRelatore,
    },
    {
      id: 'estensore',
      minWidth: 120,
      label: t('monitoraggio.estensore') as string,
      align: 'left',
      render: renderEstensore,
    },
    {
      id: 'parti',
      minWidth: 120,
      label: t('monitoraggio.parti') as string,
      align: 'left',
      render: renderParti,
    },
    {
      id: 'nrg',
      minWidth: 120,
      label: t('monitoraggio.nrg') as string,
      align: 'left',
      render: renderNrg,
    },
    {
      id: 'dataMinuta',
      minWidth: 150,
      label: t('monitoraggio.dataMinuta') as string,
      align: 'left',
      render: renderDataMinuta,
    },
    {
      id: 'dataVisionePresidente',
      minWidth: 150,
      label: t('monitoraggio.dataVisionePresidente') as string,
      align: 'left',
      render: renderDataVisionePresidente,
    },
    {
      id: 'dataRichiestaUltimaModifica',
      minWidth: 150,
      label: t('monitoraggio.dataRichiestaUltimaModifica') as string,
      align: 'left',
      render: renderDataRichiestaUltimaModifica,
    },
    {
      id: 'dataUltimaMinutaModificata',
      minWidth: 150,
      label: t('monitoraggio.dataUltimaMinutaModificata') as string,
      align: 'left',
      render: renderDataUltimaMinutaModificata,
    },
    {
      id: 'dataDepositoProvvedimento',
      minWidth: 150,
      label: t('monitoraggio.dataDepositoProvvedimento') as string,
      align: 'left',
      render: renderDataDepositoProvvedimento,
    },
    {
      id: 'dataPubblicazione',
      minWidth: 150,
      label: t('monitoraggio.dataPubblicazione') as string,
      align: 'left',
      render: renderDataPubblicazione,
    },
    {
      id: 'stato',
      minWidth: 120,
      label: t('monitoraggio.stato') as string,
      align: 'left',
    },
    {
      id: 'numeroRaccoltaGenerale',
      minWidth: 170,
      label: t('monitoraggio.numeroRaccoltaGenerale') as string,
      align: 'left',
      render: renderNumeroRaccoltaGenerale,
    },
    {
      id: 'totaleGiorni',
      minWidth: 100,
      label: t('monitoraggio.totaleGiorni') as string,
      align: 'left',
      render: renderTotaleGiorni,
    },
    {
      id: 'azioni',
      minWidth: 100,
      label: t('monitoraggio.azioni') as string,
      align: 'left',
      render: renderAzioni,
    },
  ];

  const esportaFile = async (selectedRows: any) => {
    try {
      const criteriaParameters = {
        idRicUdienza: selectedRows.map((row: any) => row.idRicUdien),
      };
      const requestPayload = {
        pageInfo: {
          pageNumber: 0,
          pageSize: 100,
          order: {
            propertyName: 'annoRicorso,numeroRicorso',
            asc: false,
          },
        },
        criteriaParameters,
      };

      try {
        setIsLoading(true);
        const api = await APIWrapper;
        await api.download(
          `monitoraggio/esporta-monitoraggio`,
          requestPayload
        );

        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        console.error('Error exporting file:', error);
      }
    } catch (error) {
      setIsLoading(false);
      console.error('Error exporting file:', error);
    }
  };

  const handleButtonClick = () => {
    esportaFile(rows);
  };

  const monitoraContent = () => {
    return (
      <>
        {rows.length > 0 && (
          <RelayTable
            columns={columns}
            rows={rows}
            loading={isLoading}
            pagination={false}
          />
        )}
        <Grid>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Box mt={4} mr={2} sx={{ width: '100%' }}>
              <Typography sx={{ mb: 1 }}>{t('monitoraggio.monitoraTable.dicituraAsterisco')}</Typography>
              <Typography>
                <span style={{ color: 'red' }}>*</span>
                <span style={{ color: 'black' }}> {t('monitoraggio.monitoraTable.dicituraAsteriscoRosso')}</span>
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <NsButton
              onClick={handleButtonClick}
              variant="contained"
              color="primary"
              sx={{ mb: 2, mt: 4 }}
            >
              {t('monitoraggio.monitoraTable.esporta')}
            </NsButton>
          </Box>
        </Grid>
      </>
    );
  };



  return (
    <>
      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
      <NsAccordion
        title={t('monitoraggio.monitoraggioTable.monitoraggioNavetta')}
        expanded={isAccordionExpanded}
        onChange={() => {
          setIsAccordionExpanded(!isAccordionExpanded);
        }}
        icon={<KeyboardArrowDownIcon sx={{ color: 'white' }} />}
        typographyProps={{
          sx: {
            fontWeight: 'bold',
            fontSize: '1.2rem',
            color: 'white',
          },
        }}
      >
        <NsAccordionDetails>{monitoraContent()}</NsAccordionDetails>
      </NsAccordion>
      <DialogModal {...modalProps} />
    </>
  );
}
