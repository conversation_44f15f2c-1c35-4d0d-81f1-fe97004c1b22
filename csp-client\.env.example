# da usare in locale puntando al container del CSP Backend
SERVIZI_CSP_URL=http://localhost:8086
# SERVIZI_CSP_URL=http://dockerpa2.netserv.it:8086
# SERVIZI_CSP_URL=http://sdm-servizi-csp-01:8080

NEXT_PUBLIC_BASE_PATH=
NEXTAUTH_SECRET=secret

DIGITAL_SIGNATURE_AUTHORITY_AZURE=https://auth03coll.giustizia.it/b2cmingiustiziaspidcoll.onmicrosoft.com/B2C_1A_SIGNIN_AAD
DIGITAL_SIGNATURE_CLIENT_ID_AZURE="1c3e3245-e9e2-490a-94d2-9116e9e184a4"
DIGITAL_SIGNATURE_REDIRECT_URI_AZURE=https://dockerpa2.netserv.it:444/firmaremota/token
DIGITAL_SIGNATURE_SCOPE_AZURE=https://b2cmingiustiziaspidcoll.onmicrosoft.com/GiustiziaAPI/csp

ENABLE_FIRMA_OTP=false

SIC_PENALE_URL="http://dockerpa2.netserv.it:8083/SICPenaleWEB/SICPenaleServlet?UC=101"

#APP DEV
AZURE_AD_B2C_CLIENT_ID="1b653aa9-30e8-4a29-a4ed-c53c725f0a09"
AZURE_AD_B2C_CLIENT_SECRET="****************************************"
AZURE_AD_B2C_TENANT_NAME="b2cmingiustiziaspidcoll"
AZURE_AD_B2C_PRIMARY_USER_FLOW="B2C_1A_SIGNIN_AAD"
AZURE_AD_B2C_HOST_NAME=auth03coll.giustizia.it
AZURE_REDIRECT_URI=https://sdm-web-deskcp-01/
MISCROSOFT_TENANT_ID=792bc8b1-9088-4858-b830-2aad443e9f3f
AZURE_SCOPE_PORTALE_DESKCASSP=https://b2cmingiustiziaspidcoll.onmicrosoft.com/GiustiziaAPI/deskcassp
AZURE_SCOPE_FIRMA_REMOTA=https://b2cmingiustiziaspidcoll.onmicrosoft.com/GiustiziaAPI/firmaremota