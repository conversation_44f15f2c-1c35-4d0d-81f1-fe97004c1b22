import { Box, Checkbox, Typography, useTheme } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import {
  NsFullPageSpinner,
  NsButton,
  useNotifier,
} from '@netservice/astrea-react-ds';
import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import APIWrapper from 'src/utils/APIWrapper';
import TipologiaProvvedimentoDeskEnum from '../enum/TipologiaProvvedimentoDeskEnum';
import { Column, MonitoraggioProps, PageInfoProps } from '../interfaces';
import MonitoraTable from './MonitoraTable';
import DetailRiuniti from '../fascicolo/DetailRiuniti';
import DialogModal from '../shared/DialogModal';
import DetailParti from '../fascicolo/DetailParti';
import DataTableWithPagination from '../shared/DataTableWithPagination';
import { getNumeroSezionaleFormatoSIC } from '../shared/Utils';

  const modalStyle = {
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 2,
  };


export default function MonitoraggioTable({
  data,
  hideFilters,
  resultMessage,
  pageInfoServer, 
  actionApplyPagination,
  actionApplySorting
}: MonitoraggioProps) {
  const { t } = useTranslation();
  const theme: any = useTheme();
  const [selected, setSelected] = useState<any[]>([]);
  const [content, setContent] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [showMonitoraTable, setShowMonitoraTable] = useState(false);
  const [showMonitoraggioTable, setShowMonitoraggioTable] = useState(true);
  const { notify } = useNotifier();

  useEffect(() => {
    if (data) {
      setContent(data);
    }
  }, [data]);


  const esportaFile = async (selectedRows: any) => {
    try {
      const criteriaParameters = {
        idRicUdienza: selectedRows.map((row: any) => row.idRicUdien),
      };

      const requestPayload = {
        pageInfo: {
          pageNumber: 0,
          pageSize: pageInfoServer.pageSize,
          order: {
            propertyName: 'annoRicorso,numeroRicorso',
            asc: false,
          },
        },
        criteriaParameters,
      };

      try {
        setIsLoading(true);
        const api = await APIWrapper;
        await api.download(`monitoraggio/esporta`, requestPayload);

        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        console.error('Error exporting file:', error);
      }
    } catch (error) {
      setIsLoading(false);
      console.error('Error exporting file:', error);
    }
  };

  const closeModal = () => {
    setModalProps({ ...modalProps, isOpen: false });
  };

  const [modalProps, setModalProps] = useState({
    isOpen: false,
    onClose: closeModal,
    title: '',
    content: <></>,
  });

  const handlePagination = useCallback((pageInfo: PageInfoProps) => {
    actionApplyPagination(pageInfo);
  }, [actionApplyPagination]);

  const handleSorting = useCallback((property: string) => {
    actionApplySorting(property);
  }, [actionApplySorting]);

  const renderSezione = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.sezione}
      </TableCell>
    );
  };

  const renderDataUdienza = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {moment(row.dataUdienza).format('DD/MM/YYYY')}
      </TableCell>
    );
  };

  const renderTipoUdienza = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.tipoUdienza}
      </TableCell>
    );
  };

  const renderCollegio = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.collegio}
      </TableCell>
    );
  };

  const renderPresidente = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.presidente}
      </TableCell>
    );
  };

  const renderRelatore = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.relatore}
      </TableCell>
    );
  };

  const renderEstensore = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.estensore}
      </TableCell>
    );
  };

  const renderParti = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>{row.parti}</Box>
          <Box>
            <NsButton
              sx={theme.custom.secondaryButton}
              onClick={() => handleModal(row.nrg)}
              variant="text"
            >
              {t('common.vedi')}
            </NsButton>
          </Box>
        </Box>
      </TableCell>
    );
  };

  const handleModal = async (data: any) => {
    const api = await APIWrapper;
    const parti = await api.call('GET', `monitoraggio/parti?nrg=${data}`);
    if (parti.error) {
      notify({ type: 'error', message: parti.error.message });
    }
    const content = <DetailParti parti={parti} />;
    const title = t('monitoraggio.monitoraggioTable.visualizzazioneParti');
    modalStyle.width = 600;

    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const handleModalSimple = async (param: string, data?: any) => {
    let content, title;
    if (param == 'riuniti') {
      content = <></>;
      modalStyle.width = 600;
      title = `(R) - Riunito al ${data.numeroPadre}/${data.annoPadre}`;
    } else if (param == 'AllRiuniti') {
      content = <DetailRiuniti idRicUdienza={data.idRicUdienza} />;
      title = t('fascicoli.fascicoliTable.visualizzazioneRiuniti');
      modalStyle.width = 600;
    } 
    else {
      content = <></>;
      title = '';
    }
    setModalProps({ ...modalProps, content, isOpen: true, title });
  };

  const renderNrg = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{ display: 'flex', alignItems: 'center' }}
          className={'ns-display-2col'}
        >
          <Box>
            {row.numeroRicorso}/{row.annoRicorso}{' '}
          </Box>
          <Box>
            {row.riunito && (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() =>
                  handleModalSimple('riuniti', row?.ricercaRiunitiView)
                }
              >
                (R)
              </NsButton>
            )}
            {row?.ricercaRiunitiView?.principale ? (
              <NsButton
                sx={theme.custom.secondaryButton}
                onClick={() =>
                  handleModalSimple('AllRiuniti', row?.ricercaRiunitiView)
                }
              >
                {t('common.principale')}
              </NsButton>
            ) : (
              ''
            )}
          </Box>
        </Box>
      </TableCell>
    );
  };

  const renderSezionale = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {getNumeroSezionaleFormatoSIC(row.numeroSezionale, row.annoSezionale)}
      </TableCell>
    );
  };

  const renderTipoProvvedimento = (cell: any, row: any) => {
    let tipo;
    switch (row.tipoRicorso) {
      case TipologiaProvvedimentoDeskEnum.SENTENZA:
        tipo = 'Sentenza';
        break;
      case TipologiaProvvedimentoDeskEnum.ORDINANZA:
        tipo = 'Ordinanza';
        break;
      case TipologiaProvvedimentoDeskEnum.MINUTASENTENZA:
        tipo = 'Minuta di Sentenza';
        break;
      case TipologiaProvvedimentoDeskEnum.MINUTAORDINANZA:
        tipo = 'Minuta di Ordinanza';
        break;
      default:
        tipo = '';
    }

    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {tipo}
        </Box>
      </TableCell>
    );
  };

  const renderTotaleGiorni = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {row.totaleGiorni}
        </Box>
      </TableCell>
    );
  };

  const handleClick = (event: any, row: any, index: number) => {
    const selectedIndex = selected.indexOf(index);
    let newSelected: number[] = [];
    let newSelectedRows: any[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, index);
      newSelectedRows = newSelectedRows.concat(selectedRows, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
      newSelectedRows = newSelectedRows.concat(selectedRows.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
      newSelectedRows = newSelectedRows.concat(selectedRows.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
      newSelectedRows = newSelectedRows.concat(
        selectedRows.slice(0, selectedIndex),
        selectedRows.slice(selectedIndex + 1)
      );
    }

    setSelected(newSelected);
    setSelectedRows(newSelectedRows);
  };

  const handleSelectAllClick = (event: any, rows: any) => {
    if (event.target.checked) {
      const newSelecteds = new Array<number>();
      const newSelectedRows = rows.filter((n: any, index: number) => {
        if (n.stato == 'RIUNITO') {
          return false;
        }
        newSelecteds.push(index);
        return true;
      });
      setSelected(newSelecteds);
      setSelectedRows(newSelectedRows);
      return;
    }
    setSelected([]);
    setSelectedRows([]);
  };

  const renderCheckbox = (cell: any, row: any, index: any) => {
    const isSelected = selected.includes(index);
    let checkbox;
    if (row.riunito) {
      checkbox = <></>;
    } else {
      checkbox = (
        <Checkbox
          color="primary"
          checked={isSelected}
          onClick={(event) => handleClick(event, row, index)}
        />
      );
    }
    return (
      <TableCell key={cell.id} sx={{ border: theme.custom.borders[0] }}>
        {checkbox}
      </TableCell>
    );
  };
  const renderStato = (column: any, row: any) => {
        let state = row.stato;
        if(row.riunito){
            state =  'Riunito';
        }
        return  <TableCell
            key={column.id}
            align={column.align}
            sx={{
                border: theme.custom.borders[0],
                background: row?.color ? row.color : '',
                color: row?.color == 'black' ? 'white' : '',
            }}
        >
            {state}
        </TableCell>;
    };

  const renderHeadCheckbox = (name: string, rows: any) => {
    return (
      <Checkbox
        color="primary"
        checked={selected.length === rows.length}
        indeterminate={selected.length > 0 && selected.length < rows.length}
        onChange={(event) => handleSelectAllClick(event, rows)}
      />
    );
  };

  const renderNumeroRaccoltaGenerale = (cell: any, row: any) => {
    return (
      <TableCell
        key={cell.id}
        align={cell.align}
        sx={{ border: theme.custom.borders[0] }}
      >
        {row.nraccg}
      </TableCell>
    );
  };

  const columns: Column[] = [
    {
      id: 'checkbox',
      label: '',
      minWidth: 50,
      align: 'left',
      render: (cell: any, row: any, index: any) => {
        return renderCheckbox(cell, row, index);
      },
      renderHeadCell(cell, rows, index) {
        return renderHeadCheckbox(cell.id, rows);
      },
      sortable: false,
    },
    {
      id: 'sezione',
      minWidth: 170,
      label: t('monitoraggio.sezione') as string,
      align: 'left',
      render: renderSezione,
      sortable: false,
    },
    {
      id: 'dataUdienza',
      align: 'left',
      label: t('monitoraggio.dataUdienza') as string,
      minWidth: 170,
      render: renderDataUdienza,
    },
    {
      id: 'tipoUdienza',
      minWidth: 170,
      label: t('monitoraggio.tipo') as string,
      align: 'left',
      render: renderTipoUdienza,
    },
    {
      id: 'collegio',
      minWidth: 170,
      label: t('monitoraggio.collegio') as string,
      align: 'right',
      render: renderCollegio,
      sortable: false,
    },
    {
      id: 'presidente',
      minWidth: 170,
      label: t('monitoraggio.presidente') as string,
      align: 'left',
      render: renderPresidente,
      sortable: false,
    },
    {
      id: 'relatore',
      minWidth: 170,
      label: t('monitoraggio.relatore') as string,
      align: 'left',
      render: renderRelatore,
      sortable: false,
    },
    {
      id: 'estensore',
      minWidth: 170,
      label: t('monitoraggio.estensore') as string,
      align: 'left',
      render: renderEstensore,
      sortable: false,
    },
    {
      id: 'detParti',
      minWidth: 170,
      label: t('monitoraggio.parti') as string,
      align: 'left',
      render: renderParti,
      sortable: false,
    },
    {
      id: 'nrg',
      minWidth: 170,
      label: t('monitoraggio.nrg') as string,
      align: 'left',
      render: renderNrg,
    },
    {
      id: 'numeroSezionale',
      minWidth: 170,
      label: t('monitoraggio.sezionale') as string,
      align: 'left',
      render: renderSezionale,
    },
    {
      id: 'tipoRicorso',
      minWidth: 170,
      label: t('monitoraggio.tipoProvvedimento') as string,
      align: 'left',
      render: renderTipoProvvedimento,
      sortable: false,
    },
    {
      id: 'stato',
      minWidth: 170,
      label: t('monitoraggio.stato') as string,
      align: 'left',
      render: renderStato,
      sortable: false,
    },
    {
      id: 'nraccg',
      minWidth: 170,
      label: t('monitoraggio.numeroRaccoltaGenerale') as string,
      align: 'left',
      render: renderNumeroRaccoltaGenerale,
    },
    {
      id: 'totaleGiorni',
      minWidth: 170,
      label: t('monitoraggio.totaleGiorni') as string,
      align: 'left',
      render: renderTotaleGiorni,
      sortable: false,
    },
  ];

  const hide = () => {
    hideFilters();
  };

  return (
    <>
      {showMonitoraggioTable && (
        <DataTableWithPagination
          rows={content}
          resultMessage={resultMessage}
          columns={columns}
          actionChangePage={handlePagination}
          pageInfoServer={pageInfoServer}
          addPaginate={true}
          sorting={true}
          createSortHandler={handleSorting}
        />
      )}
      <DialogModal {...modalProps} />

      {isLoading && <NsFullPageSpinner isOpen={isLoading} value={100} />}
      {!showMonitoraTable && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <Box>
            <Typography sx={{ mb: 1 }}>
              {t('monitoraggio.monitoraggioTable.dicituraAsterisco')}
            </Typography>
            <Typography>
              <span style={{ color: 'red' }}>*</span>
              <span style={{ color: 'black' }}> {t('monitoraggio.monitoraTable.dicituraAsteriscoRosso')}</span>
            </Typography>
          </Box>

          <Box ml={2} display="flex">
            <NsButton
              variant="contained"
              color="primary"
              onClick={() => {
                esportaFile(selectedRows);
              }}
              disabled={selectedRows.length === 0}
            >
              {t('monitoraggio.monitoraggioTable.esporta')}
            </NsButton>
            <NsButton
              variant="contained"
              color="primary"
              sx={{ marginLeft: '20px' }}
              onClick={() => {
                setShowMonitoraTable(true);
                setShowMonitoraggioTable(false);
                hide();
              }}
              disabled={selectedRows.length === 0}
            >
              {t('monitoraggio.monitoraggioTable.monitora')}
            </NsButton>
          </Box>
        </Box>
      )}
      {showMonitoraTable && <MonitoraTable data={selectedRows} />}
    </>
  );
}
