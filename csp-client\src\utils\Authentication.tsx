import jwtDecode from 'jwt-decode';
import { NextRouter, useRouter } from 'next/router';
import React, { ComponentType, useEffect } from 'react';
import { Msal } from './Msal';

export interface UserData {
  nome: string;
  cognome: string;
  idSezione: string;
  siglaSezione: string;
  idProfilo: string;
  sentenzaMinuta: boolean;
  depositoSentenza: boolean;
  numRaccGenerale: boolean;
  gestionePagamenti: boolean;
  admin: boolean;
  idSezioni: number[];
  sigleSezioni: string[];
  idSezioneRG: number;
  idUtente: number;
  codiceFiscale: string;
  verificaDati: boolean;
  configEnabled: boolean;
  verificaPagamenti: boolean;
  ruoloUdienza: boolean;
  idSezioneSesta: number;
  readOnly: boolean;
}

// const cookieManager = new Cookies()
let token: string | undefined;
let userData: UserData;

export function getUserData(): UserData {
  if (!userData) {
    const storedToken = getStoredToken();
    if (storedToken) {
      userData = jwtDecode<UserData>(storedToken || '');
    }
  }
  return userData;
}

export function pubblicazioneDisabled(): boolean {
  const userData = getUserData();
  return !userData?.depositoSentenza && !userData?.numRaccGenerale;
}

export function getIdSezione() {
  return getUserData()?.idSezione;
}

export function getIdProfilo() {
  return getUserData()?.idProfilo;
}

export function getCodiceFiscale() {
  return getUserData().codiceFiscale;
}

export function getIdUtente() {
  return getUserData()?.idUtente;
}

export function hasSezione(idSezione: any) {
  return isAdmin() || getUserData()?.idSezioni.includes(Number(idSezione));
}

export function getSigleSezioni() {
  return getUserData()?.sigleSezioni;
}

export function isUtenteRegistroGenerale() {
  return getUserData()?.idSezioneRG > 0;
}

export function isAdmin() {
  return getUserData()?.admin;
}

export function isDepositoSentenza() {
  return getUserData()?.depositoSentenza || isAdmin();
}

export function isSentenzaMinuta() {
  return getUserData()?.sentenzaMinuta || isAdmin();
}

export function isGestionePagamenti() {
  return getUserData()?.gestionePagamenti || isAdmin();
}

export function isVerificaDati() {
  return getUserData()?.verificaDati || isAdmin();
}

export function isConfigEnabled() {
  return getUserData()?.configEnabled || isAdmin();
}

export function isVerificaPagamenti() {
  return getUserData()?.verificaPagamenti || isAdmin();
}
export function getSiglaSezione() {
  return getUserData()?.siglaSezione;
}

export function isRuoloUdienza() {
  return (
    (getUserData()?.ruoloUdienza && getUserData()?.idSezioneSesta > 0) ||
    isAdmin()
  );
}

export function getStoredToken(): string | undefined {
  // return cookieManager.get('authToken')
  return token;
}

export function storeToken(newToken: string): void {
  // cookieManager.set('authToken', token)
  token = newToken;
}

export function removeStoredToken(): void {
  // cookieManager.remove('authToken')
  token = undefined;
}

export function AuthenticatedComponentWrapper(props: any) {
  const router = useRouter();
  const storedToken = getStoredToken();

  useEffect(() => {
    if (!storedToken) {
      router.push('/');
    }
  }, [router, storedToken]);

  return React.createElement(props.component, props);
}

export function authenticated(component: ComponentType<any>) {
  return function Wrapper(props: any) {
    return <AuthenticatedComponentWrapper component={component} {...props} />;
  };
}

export function isReadOnly() {
  return getUserData()?.readOnly;
}

export const clearLocal = () => {
  localStorage.clear();
  document.cookie = '';
};

export const logout = (router: NextRouter, push: string | undefined | null) => {
  if (Msal.session()) Msal.popLogout();
  if (localStorage.sicUrl) {
    const fromUrl = `${localStorage.sicUrl}`;
    localStorage.removeItem('sicUrl');
    router.push(fromUrl);
    clearLocal();
    return;
  }
  if (push) router.push(push);
  else router.reload();
  clearLocal();
};
