import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { FormValueStateReturn } from 'relay-forms';
import {
  MenuItemProps,
  TipoMap,
  mapEsitiAndDiagnosiProps,
} from '../interfaces';

export function isAnno<ValueType>(message: React.ReactNode) {
  return (v: ValueType) =>
    (!v ||
      (Array.isArray(v) && (v as any).length === 0) ||
      ((v as string).length === 4 && /^\d{4}$/.test(v as string))
      ? undefined
      : message) as string | undefined;
}

export function rangeValidatorFactory(targetField: string) {
  return (message: React.ReactNode) =>
    (endDate: string, deps?: { [key: string]: FormValueStateReturn<any> }) => {
      const targetValue = deps?.[targetField]?.value;
      if (targetValue) {
        const start = moment(targetValue, 'DD/MM/YYYY');
        const end = moment(endDate, 'DD/MM/YYYY');
        if (end.isBefore(start)) {
          return message as string;
        }
      }
      return undefined;
    };
}

export function isRangeValid<ValueType>(message: React.ReactNode) {
  return (
    endDate: ValueType,
    deps?: { [key: string]: FormValueStateReturn<any> }
  ) => {
    if (deps?.['dataUdienzaDa']) {
      const { value } = deps['dataUdienzaDa'];
      if (value && value.length > 0) {
        const start = moment(value, 'DD/MM/YYYY');
        const end = moment(endDate as string, 'DD/MM/YYYY');
        if (end.isBefore(start)) {
          return message as string;
        }
      }
    }

    return undefined;
  };
}

export const formatDate = (dateString: string, typeFormat: string): string => {
  const date = moment(dateString);
  return date.format(typeFormat);
};

export function convertDateFormat(dateString: any) {
  if (dateString) {
    const [day, month, year] = dateString.split('/');
    return new Date(year, month - 1, day);
  } else {
    return null;
  }
}

export function getDateInMilliseconds(dateString: any) {
  const date = convertDateFormat(dateString);
  date?.setHours(0, 0, 0, 0);
  return date?.toISOString() ?? null;
}

export const getStyledModal = (theme: any) => ({
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: theme.custom.borders[0],
  boxShadow: 24,
  p: 2,
});

export const mapEsitiAndDiagnosi = (data: any) => {
  const colors: any = {
    WARN: '#fffc0061',
    ERROR: '#d4351cbd',
    FATAL: '#00000075',
  };
  const map: mapEsitiAndDiagnosiProps = { esiti: [], diagnosi: [] };
  const diagnosi: any[] = data.flatMap((d: any) => d.esiti);

  const esiti = data.map((d: any) => {
    delete d.esiti;
    return d;
  });
  map.esiti = [...esiti];

  diagnosi.map((d: any) => {
    if (d?.data) {
      d.data = formatDate(d.data, 'DD/MM/YYYY HH:mm:ss');
    }
    // Check if d and d.livello exist before accessing colors
    if (d && d.livello) {
      d.color = colors[d.livello];
    }
  });
  map.diagnosi = [...diagnosi];
  return map;
};

export const tipoMap: TipoMap = {
  DA: 'DATI ATTO',
  ATTO: 'ATTO PRINCIPALE',
  SM: 'ALLEGATO',
};

export const getStateNames = (role: string, state: string) => {
  const customStates: Record<string, string> = {
    ...stateMap,
    MINUTA_ACCETTATA:
      role === 'PRESIDENTE'
        ? 'Minuta pervenuta'
        : 'Minuta accettata e inviata a Presidente',
    CODA_DI_FIRMA:
      role === 'PRESIDENTE'
        ? 'In coda di firma'
        : 'Minuta accettata e inviata a Presidente',
  };
  return customStates[state];
};

export const GetDiagnosyColumns = (param: string = '') => {
  const { t } = useTranslation();

  const storieColumns: any[] = [
    {
      id: 'data',
      label: t('depositi.detailDeposito.dataEOra'),
    },
    {
      id: 'descrizione',
      label: t('depositi.detailDeposito.descrizione'),
    },
    {
      id: 'note',
      label: t('depositi.detailDeposito.note'),
    }
  ];

  const columns: any[] = [
    {
      id: 'data',
      label: t('shared.sideModal.dataOra'),
    },
    {
      id: 'codice',
      label: t('shared.sideModal.evento'),
    },
    {
      id: 'tipoContent',
      label: t('shared.sideModal.tipologiaEvento'),
    },
    {
      id: 'descrizione',
      label: t('shared.sideModal.descrizione'),
    },
  ];

  if (param === 'stories') {
    return storieColumns;
  } else {
    return columns;
  }
};

export const getSezioniOptions = (): MenuItemProps[] => {
  return [
    { value: 'S1', label: 'S1' },
    { value: 'S2', label: 'S2' },
    { value: 'S3', label: 'S3' },
    { value: 'S4', label: 'S4' },
    { value: 'S5', label: 'S5' },
    { value: 'S6', label: 'S6' },
    { value: 'S7', label: 'S7' },
    { value: 'SU', label: 'SU' },
    { value: 'SF', label: 'SF' },
  ];
};

export const getCollegioOptions = (): MenuItemProps[] => {
  return [
    { value: '0', label: '0' },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
  ];
};

export const tipoUdienzeOptions = (): MenuItemProps[] => {
  return [
    { value: 'CC', label: 'CC' },
    { value: 'PU', label: 'PU' },
  ];
};

export const stateMap: Record<string, string> = {
  BOZZA: 'In bozza',
  IN_BOZZA: 'In bozza',
  IN_CODE_FIRMA_REL: 'In coda di firma relatore',
  INVIATO_IN_CANCELLERIA_RELATORE: 'Inviato in cancelleria',
  ACCETTATO: 'Accettato',
  RIFIUTATO: 'Rifiutato',
  NEW: 'Nuovo',
  BUSTA_RIFIUTATA: 'Busta rifiutata',
  MINUTA_ACCETTATA: 'Minuta accettata e inoltrata al Presidente',
  MINUTA_DA_MODIFICARE: 'Richiesta modifica',
  MINUTA_MODIFICATA: 'Minuta modificata',
  INVIATO_IN_CANCEL_PRESIDENTE: 'Inviato in cancelleria dal Presidente',
  PUBBLICATA: 'Pubblicato',
  PROVV_DEPOSITATO_SIC: 'Provvedimento depositato da SIC',
  PUBBLICATO_SIC: 'Pubblicato dal SIC',
  MINUTA_DEPOSITATA_SIC: 'Minuta depositata da SIC',
  ERRORE_DI_PUBBLICAZIONE: 'Errore di pubblicazione',
  MINUTA_DEPOSITATA: 'Minuta depositata',
  CODA_DI_FIRMA: 'In coda di firma',
  MINUTA_MODIFICATA_PRESIDENTE: "Minuta modificata e inoltrata all'estensore",
  RIUNITO: 'RIUNITO',
  RIUNITO_CARTACEO: 'Da redigere in cartaceo',
  BOZZA_PRESIDENTE: 'Bozza del Presidente'
};

export const tipoProvvedimentoMap: Record<string, string> = {
  SENTENZA: 'Sentenza',
  ORDINANZA: 'Ordinanza',
  MINUTASENTENZA: 'Minuta di Sentenza',
  MINUTAORDINANZA: 'Minuta di Ordinanza',
};

export const aggiustaDataDa = (data: string): string | null => {
  if (!data) return null;
  if (data.match(/^GG\/MM\/\d{4}$/))
    return data.replace(/^GG\/MM\/(\d{4})$/, '01/01/$1');
  else if (data.match(/^GG\/\d{2}\/\d{4}$/))
    return data.replace(/^GG\/(\d{2})\/(\d{4})$/, '01/$1/$2');
  else if (data.match(/^\d{2}\/\d{2}\/\d{4}$/))
    return data;
  return null;
}

export const aggiustaDataA = (data: string): string | null => {
  if (!data) return null;
  if (data.match(/^GG\/MM\/\d{4}$/)) {
    data = data.replace(/^GG\/MM\/(\d{4})$/, '01/01/$1');
    let momentDate = moment(data, 'DD/MM/YYYY');
    momentDate.add(1, 'year');
    data = momentDate.format('DD/MM/YYYY');
    return data;
  }

  else if (data.match(/^GG\/\d{2}\/\d{4}$/)) {
    data = data.replace(/^GG\/(\d{2})\/(\d{4})$/, '01/$1/$2');
    let momentDate = moment(data, 'DD/MM/YYYY');
    momentDate.add(1, 'month');
    data = momentDate.format('DD/MM/YYYY');
    return data;
  }
  else if (data.match(/^d{2}\/\d{2}\/\d{4}$/))
    return data;
  return null;
}

export const getNumeroSezionaleFormatoSIC = (numeroSezionale: number, annoSezionale: number): string => {
  const numSez = numeroSezionale.toString();
  const bis = numSez.substring(numSez.length - 3);
  const numSezFirstPart = numSez.slice(0, -3);
  return `${numSezFirstPart} - ${annoSezionale} - ${bis}`;
}